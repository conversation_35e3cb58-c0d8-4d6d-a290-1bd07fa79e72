#!/bin/bash

# 本地开发环境启动脚本
# 使用local profile启动，加载本地开发配置

echo "=== 本地开发环境启动 ==="
echo "Profile: local"
echo "配置: application.properties + application-local.properties"
echo "时间: $(date)"
echo "========================"

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保已安装Java 8或更高版本"
    exit 1
fi

echo "正在启动应用..."
echo ""

# 使用Gradle启动，指定local profile
./gradlew bootRun -Pprofile=local

echo ""
echo "应用已停止"
