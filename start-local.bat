@echo off
chcp 65001 >nul

REM 本地开发环境启动脚本 (Windows)
REM 使用local profile启动，加载本地开发配置

echo === 本地开发环境启动 ===
echo Profile: local
echo 配置: application.properties + application-local.properties
echo 时间: %date% %time%
echo ========================

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 8或更高版本
    pause
    exit /b 1
)

echo.
echo 正在启动应用...
echo.

REM 使用Gradle启动，指定local profile
gradlew.bat bootRun -Pprofile=local

echo.
echo 应用已停止
pause
