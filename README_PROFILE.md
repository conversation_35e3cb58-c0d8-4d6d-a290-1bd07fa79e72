# Profile 配置快速指南

## 🎯 目标
实现本地开发和服务端部署的配置分离，本地调试时使用 `local` profile，服务端保持原有方式不变。

## 🚀 快速开始

### 本地开发
```bash
# 方式1: 使用启动脚本（推荐）
./start-local.sh

# 方式2: 使用Gradle命令
./gradlew bootRun -Pprofile=local
```

### 服务端部署
```bash
# 保持原有方式不变
./gradlew bootRun
# 或
java -jar manager.war
```

## 📁 配置文件
- `application.properties` - 主配置文件（默认配置）
- `application-local.properties` - 本地开发配置（覆盖部分默认配置）

## ✅ 验证
启动时会显示当前模式：
- **本地模式**: "=== 本地开发模式启动 === Profile: local"
- **默认模式**: "=== 默认模式启动 === 使用原始 application.properties 配置"

## 📖 详细文档
查看 [PROFILE_GUIDE.md](./PROFILE_GUIDE.md) 了解完整配置说明。
