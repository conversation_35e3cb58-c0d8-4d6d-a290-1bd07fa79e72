# Redis统一使用方案

## 📋 方案概述

本方案旨在统一项目中的Redis客户端使用，将所有Redis操作迁移到Redisson，提供更好的性能、稳定性和分布式功能支持。

## 🎯 目标

1. **统一客户端**：使用Redisson作为唯一Redis客户端
2. **提升性能**：利用Redisson的连接池和异步特性
3. **增强功能**：支持分布式锁、分布式集合等高级功能
4. **简化维护**：减少依赖冲突，统一配置管理

## 📊 现状分析

### 当前依赖
```gradle
implementation('org.springframework.boot:spring-boot-starter-data-redis')
implementation('org.redisson:redisson-spring-boot-starter:3.17.7')
// implementation("redis.clients:jedis") // 已注释但仍有直接使用
```

### 直接使用Jedis的模块
1. **巡检模块** (`RedisInspector.java`, `RedisInspect.java`)
2. **故障转移** (`RedisFailover.java`)
3. **集群策略** (`RedisClusterStrategy.java`)
4. **工具类** (`ClusterUtil.java`)

## 🔧 迁移策略

### 阶段一：创建统一Redis工具类
创建基于Redisson的Redis工具类，提供与Jedis兼容的API。

### 阶段二：逐步迁移现有代码
1. 巡检模块迁移
2. 故障转移模块迁移
3. 集群策略迁移
4. 其他工具类迁移

### 阶段三：清理和优化
1. 移除Jedis相关依赖
2. 统一配置管理
3. 性能优化

## 📁 实施计划

### 1. 创建统一Redis服务类
```java
@Service
public class UnifiedRedisService {
    private final RedissonClient redissonClient;
    private final RedisTemplate<String, Object> redisTemplate;
    
    // 提供统一的Redis操作接口
    public String getInfo(String section);
    public boolean testConnection(String host, int port, String username, String password);
    public Map<String, String> getRedisInfo(RedisConfigDTO config);
}
```

### 2. 配置优化
- 统一Redis连接配置
- 优化连接池参数
- 支持单机/哨兵/集群模式

### 3. 兼容性保证
- 保持现有API接口不变
- 渐进式迁移，确保系统稳定

## ⚠️ 风险评估

### 低风险
- Job模块：已使用Redisson，无需迁移
- 新功能开发：直接使用统一服务

### 中风险
- 巡检模块：需要仔细测试Redis连接和信息获取
- 配置管理：需要确保配置兼容性

### 高风险
- 故障转移：关键功能，需要充分测试
- 集群策略：涉及Redis主从判断，需要验证准确性

## 📈 预期收益

1. **性能提升**：Redisson基于Netty，性能更优
2. **功能增强**：支持分布式锁、分布式集合等
3. **维护简化**：统一客户端，减少依赖管理复杂度
4. **稳定性提升**：更好的连接池管理和故障恢复

## 🚀 实施时间线

- **第1周**：创建统一Redis服务类和工具类
- **第2周**：迁移巡检模块
- **第3周**：迁移故障转移和集群策略
- **第4周**：测试验证和性能优化
- **第5周**：清理旧代码和文档更新

## ✅ 验证标准

1. **功能验证**：所有Redis相关功能正常工作
2. **性能验证**：响应时间不劣化，最好有提升
3. **稳定性验证**：长时间运行无异常
4. **兼容性验证**：支持单机/哨兵/集群模式
