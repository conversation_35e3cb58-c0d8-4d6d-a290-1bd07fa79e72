# 测试环境配置
# Redis功能明确禁用
redis.enabled=false

# 本地开发路径配置
base.dir=./
k8s.config.path=${base.dir}/config_56_thirddev

# 禁用Kubernetes相关功能（使用第三方K8s开关）
third.k8s.switch=true

# 禁用不必要的自动配置
spring.autoconfigure.exclude=\
  org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,\
  org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration,\
  org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration

# 日志配置
logging.level.com.xylink=DEBUG
logging.level.org.springframework.boot.autoconfigure=DEBUG
