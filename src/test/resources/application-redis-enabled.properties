# Redis启用测试环境配置
# Redis功能启用
redis.enabled=true

# Redis连接配置（连接到Docker测试实例）
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms
spring.redis.database=0
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

# 本地开发路径配置
base.dir=./
k8s.config.path=${base.dir}/config_56_thirddev

# 禁用Kubernetes相关功能（避免测试时连接问题）
k8s.enabled=false
k8s.config.enabled=false

# 日志配置
logging.level.com.xylink=DEBUG
logging.level.org.springframework.boot.autoconfigure=DEBUG
logging.level.org.springframework.data.redis=DEBUG
