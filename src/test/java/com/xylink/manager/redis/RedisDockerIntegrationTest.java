package com.xylink.manager.redis;

import com.xylink.manager.service.redis.RedisAvailabilityService;
import com.xylink.manager.service.redis.UnifiedRedisService;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis Docker集成测试
 * 
 * 使用Docker Redis实例进行真实的Redis功能测试
 * 
 * 前置条件：需要启动Docker Redis容器
 * docker run --name redis-test -p 6379:6379 -d redis:7-alpine
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class RedisDockerIntegrationTest {

    /**
     * 测试Redis禁用场景
     */
    @Nested
    @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
    @ActiveProfiles("test")
    @TestPropertySource(properties = {
        "redis.enabled=false",
        "k8s.enabled=false",
        "k8s.config.enabled=false"
    })
    @DisplayName("Redis禁用测试")
    class RedisDisabledTests {

        @Autowired
        private ApplicationContext applicationContext;

        @Autowired
        private RedisAvailabilityService redisAvailabilityService;

        @Test
        @Order(1)
        @DisplayName("验证Redis禁用时Bean不被创建")
        public void testRedisDisabledBeanCreation() {
            // 验证Redis相关Bean不应该被创建
            assertFalse(applicationContext.containsBean("unifiedRedisService"), 
                "UnifiedRedisService should not be created when redis.enabled=false");
            
            // 验证RedisAvailabilityService正常创建
            assertNotNull(redisAvailabilityService, 
                "RedisAvailabilityService should always be available");
            
            // 验证Redis不可用
            assertFalse(redisAvailabilityService.isRedisAvailable(), 
                "Redis should not be available when redis.enabled=false");
        }

        @Test
        @Order(2)
        @DisplayName("验证Redis禁用时的降级行为")
        public void testRedisDisabledFallbackBehavior() {
            // 测试连接测试
            boolean connectionResult = redisAvailabilityService.testConnection("localhost", 6379, null, null);
            assertFalse(connectionResult, "Connection test should return false when Redis is disabled");
            
            // 测试PING命令
            String pingResult = redisAvailabilityService.ping();
            assertEquals("Redis不可用", pingResult, "Ping should return 'Redis不可用' when Redis is disabled");
            
            // 测试版本获取
            String version = redisAvailabilityService.getRedisVersion();
            assertEquals("unknown", version, "Version should return 'unknown' when Redis is disabled");
            
            // 测试获取Redis信息
            Map<String, String> redisInfo = redisAvailabilityService.getRedisInfo(null);
            assertNotNull(redisInfo, "Redis info should not be null");
            assertTrue(redisInfo.isEmpty(), "Redis info should be empty when Redis is disabled");
        }
    }

    /**
     * 测试Redis启用场景（需要Docker Redis实例）
     */
    @Nested
    @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
    @ActiveProfiles("redis-enabled")
    @TestPropertySource(properties = {
        "redis.enabled=true",
        "spring.redis.host=localhost",
        "spring.redis.port=6379",
        "k8s.enabled=false",
        "k8s.config.enabled=false"
    })
    @DisplayName("Redis启用测试")
    class RedisEnabledTests {

        @Autowired
        private ApplicationContext applicationContext;

        @Autowired
        private RedisAvailabilityService redisAvailabilityService;

        @Autowired(required = false)
        private UnifiedRedisService unifiedRedisService;

        @Test
        @Order(1)
        @DisplayName("验证Redis启用时Bean被正确创建")
        public void testRedisEnabledBeanCreation() {
            // 验证RedisAvailabilityService正常创建
            assertNotNull(redisAvailabilityService, 
                "RedisAvailabilityService should be available");
            
            // 验证UnifiedRedisService被创建
            assertNotNull(unifiedRedisService, 
                "UnifiedRedisService should be created when redis.enabled=true");
            
            // 验证Redis被标记为启用
            assertTrue(redisAvailabilityService.isRedisAvailable(), 
                "Redis should be available when redis.enabled=true");
        }

        @Test
        @Order(2)
        @DisplayName("验证Redis连接功能")
        public void testRedisConnection() {
            // 测试连接
            boolean connectionResult = redisAvailabilityService.testConnection("localhost", 6379, null, null);
            assertTrue(connectionResult, "Should be able to connect to Docker Redis instance");
            
            // 测试PING命令
            String pingResult = redisAvailabilityService.ping();
            assertEquals("PONG", pingResult, "Ping should return 'PONG' when Redis is available");
        }

        @Test
        @Order(3)
        @DisplayName("验证Redis信息获取功能")
        public void testRedisInfo() {
            // 测试版本获取
            String version = redisAvailabilityService.getRedisVersion();
            assertNotNull(version, "Version should not be null");
            assertNotEquals("unknown", version, "Version should not be 'unknown' when Redis is available");
            assertTrue(version.contains("."), "Version should contain version numbers");
            
            // 测试获取Redis信息
            Map<String, String> redisInfo = redisAvailabilityService.getRedisInfo("server");
            assertNotNull(redisInfo, "Redis info should not be null");
            assertFalse(redisInfo.isEmpty(), "Redis info should not be empty when Redis is available");
            
            // 验证包含版本信息
            assertTrue(redisInfo.containsKey("redis_version") || 
                      redisInfo.values().stream().anyMatch(v -> v.contains("redis_version")),
                      "Redis info should contain version information");
        }

        @Test
        @Order(4)
        @DisplayName("验证Redis内存和统计信息")
        public void testRedisMemoryAndStats() {
            // 测试内存信息
            Map<String, String> memoryInfo = redisAvailabilityService.getMemoryInfo();
            assertNotNull(memoryInfo, "Memory info should not be null");
            // 注意：内存信息可能为空，这取决于Redis版本和配置
            
            // 测试统计信息
            Map<String, String> statsInfo = redisAvailabilityService.getStatsInfo();
            assertNotNull(statsInfo, "Stats info should not be null");
            // 注意：统计信息可能为空，这取决于Redis版本和配置
        }

        @Test
        @Order(5)
        @DisplayName("验证Redis主节点检查")
        public void testRedisMasterCheck() {
            // 测试主节点检查（单机Redis应该返回true）
            boolean isMaster = redisAvailabilityService.isRedisMaster("localhost", 6379, null, null);
            assertTrue(isMaster, "Single Redis instance should be considered as master");
        }
    }

    /**
     * 清理方法：在所有测试完成后停止Redis容器
     */
    @AfterAll
    static void cleanup() {
        try {
            // 停止并删除Redis测试容器
            ProcessBuilder pb = new ProcessBuilder("docker", "stop", "redis-test");
            Process process = pb.start();
            process.waitFor();
            
            pb = new ProcessBuilder("docker", "rm", "redis-test");
            process = pb.start();
            process.waitFor();
            
            System.out.println("Redis test container cleaned up");
        } catch (Exception e) {
            System.err.println("Failed to cleanup Redis container: " + e.getMessage());
        }
    }
}
