package com.xylink.manager.redis;

import com.xylink.manager.service.redis.RedisAvailabilityService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis弱关联配置测试
 * 
 * 验证在不同配置下Redis服务的行为：
 * 1. redis.enabled=false时，Redis相关Bean不应该被创建
 * 2. RedisAvailabilityService应该正常工作并提供降级功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class RedisWeakCouplingTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private RedisAvailabilityService redisAvailabilityService;

    @Test
    public void testRedisDisabledConfiguration() {
        // 验证Redis功能被禁用时，UnifiedRedisService不应该被创建
        assertFalse(applicationContext.containsBean("unifiedRedisService"), 
            "UnifiedRedisService should not be created when redis.enabled=false");
        
        // 验证RedisAvailabilityService正常创建
        assertNotNull(redisAvailabilityService, 
            "RedisAvailabilityService should always be available");
        
        // 验证Redis不可用
        assertFalse(redisAvailabilityService.isRedisAvailable(), 
            "Redis should not be available when redis.enabled=false");
    }

    @Test
    public void testRedisAvailabilityServiceFallback() {
        // 测试Redis不可用时的降级行为
        
        // 测试连接测试
        boolean connectionResult = redisAvailabilityService.testConnection("localhost", 6379, null, null);
        assertFalse(connectionResult, "Connection test should return false when Redis is disabled");
        
        // 测试获取Redis信息
        java.util.Map<String, String> redisInfo = redisAvailabilityService.getRedisInfo(null);
        assertNotNull(redisInfo, "Redis info should not be null");
        assertTrue(redisInfo.isEmpty(), "Redis info should be empty when Redis is disabled");

        // 测试主节点检查
        boolean isMaster = redisAvailabilityService.isRedisMaster("localhost", 6379, null, null);
        assertFalse(isMaster, "Master check should return false when Redis is disabled");

        // 测试内存信息
        java.util.Map<String, String> memoryInfo = redisAvailabilityService.getMemoryInfo();
        assertNotNull(memoryInfo, "Memory info should not be null");
        assertTrue(memoryInfo.isEmpty(), "Memory info should be empty when Redis is disabled");

        // 测试统计信息
        java.util.Map<String, String> statsInfo = redisAvailabilityService.getStatsInfo();
        assertNotNull(statsInfo, "Stats info should not be null");
        assertTrue(statsInfo.isEmpty(), "Stats info should be empty when Redis is disabled");
        
        // 测试PING命令
        String pingResult = redisAvailabilityService.ping();
        assertEquals("Redis不可用", pingResult, "Ping should return 'Redis不可用' when Redis is disabled");
        
        // 测试版本获取
        String version = redisAvailabilityService.getRedisVersion();
        assertEquals("unknown", version, "Version should return 'unknown' when Redis is disabled");
    }

    @Test
    public void testRedisConfigurationBeans() {
        // 验证Redis相关的配置Bean不应该被创建
        assertFalse(applicationContext.containsBean("jobRedisConfig"), 
            "JobRedisConfig should not be active when redis.enabled=false");
        
        assertFalse(applicationContext.containsBean("redissonClient"), 
            "RedissonClient should not be created when redis.enabled=false");
        
        assertFalse(applicationContext.containsBean("redisTemplate"), 
            "RedisTemplate should not be created when redis.enabled=false");
        
        assertFalse(applicationContext.containsBean("redisConnectionFactory"), 
            "RedisConnectionFactory should not be created when redis.enabled=false");
    }

    @Test
    public void testApplicationStartsWithoutRedis() {
        // 验证应用能够在没有Redis的情况下正常启动
        assertNotNull(applicationContext, "Application context should be available");
        
        // 验证关键服务仍然可用
        assertNotNull(redisAvailabilityService, "RedisAvailabilityService should be available");
        
        // 验证应用没有因为Redis不可用而启动失败
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        assertTrue(beanNames.length > 0, "Application should have loaded beans successfully");
    }
}
