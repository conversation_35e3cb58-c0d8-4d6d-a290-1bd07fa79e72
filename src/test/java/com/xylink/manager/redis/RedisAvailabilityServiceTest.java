package com.xylink.manager.redis;

import com.xylink.manager.service.redis.RedisAvailabilityService;
import com.xylink.manager.service.redis.UnifiedRedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * Redis可用性服务单元测试
 * 
 * 测试Redis弱关联功能的核心逻辑
 */
public class RedisAvailabilityServiceTest {

    private RedisAvailabilityService redisAvailabilityService;

    @Mock
    private UnifiedRedisService unifiedRedisService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        redisAvailabilityService = new RedisAvailabilityService();
    }

    @Test
    public void testRedisDisabled() {
        // 模拟Redis被禁用的情况
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", false);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", null);

        // 验证Redis不可用
        assertFalse(redisAvailabilityService.isRedisAvailable());

        // 验证降级行为
        assertFalse(redisAvailabilityService.testConnection("localhost", 6379, null, null));
        
        Map<String, String> redisInfo = redisAvailabilityService.getRedisInfo(null);
        assertNotNull(redisInfo);
        assertTrue(redisInfo.isEmpty());

        assertFalse(redisAvailabilityService.isRedisMaster("localhost", 6379, null, null));

        Map<String, String> memoryInfo = redisAvailabilityService.getMemoryInfo();
        assertNotNull(memoryInfo);
        assertTrue(memoryInfo.isEmpty());

        Map<String, String> statsInfo = redisAvailabilityService.getStatsInfo();
        assertNotNull(statsInfo);
        assertTrue(statsInfo.isEmpty());

        assertEquals("Redis不可用", redisAvailabilityService.ping());
        assertEquals("unknown", redisAvailabilityService.getRedisVersion());
    }

    @Test
    public void testRedisEnabled() {
        // 模拟Redis被启用的情况
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", true);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", unifiedRedisService);

        // 验证Redis可用
        assertTrue(redisAvailabilityService.isRedisAvailable());

        // 模拟Redis服务正常工作
        when(unifiedRedisService.testConnection(anyString(), anyInt(), any(), any())).thenReturn(true);
        when(unifiedRedisService.isRedisMaster(anyString(), anyInt(), any(), any())).thenReturn(true);
        when(unifiedRedisService.ping()).thenReturn("PONG");
        when(unifiedRedisService.getRedisVersion()).thenReturn("6.2.6");

        // 验证正常行为
        assertTrue(redisAvailabilityService.testConnection("localhost", 6379, null, null));
        assertTrue(redisAvailabilityService.isRedisMaster("localhost", 6379, null, null));
        assertEquals("PONG", redisAvailabilityService.ping());
        assertEquals("6.2.6", redisAvailabilityService.getRedisVersion());
    }

    @Test
    public void testRedisEnabledButServiceUnavailable() {
        // 模拟Redis启用但服务不可用的情况
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", true);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", unifiedRedisService);

        // 模拟Redis服务抛出异常
        when(unifiedRedisService.testConnection(anyString(), anyInt(), any(), any()))
            .thenThrow(new RuntimeException("Redis connection failed"));

        // 验证降级行为
        assertFalse(redisAvailabilityService.testConnection("localhost", 6379, null, null));
    }

    @Test
    public void testExecuteWithFallback() {
        // 测试executeWithFallback方法
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", false);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", null);

        String result = redisAvailabilityService.executeWithFallback(
            redis -> "Redis result",
            "Default value"
        );

        assertEquals("Default value", result);
    }

    @Test
    public void testExecuteOptional() {
        // 测试executeOptional方法
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", false);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", null);

        java.util.Optional<String> result = redisAvailabilityService.executeOptional(
            redis -> "Redis result"
        );

        assertFalse(result.isPresent());
    }

    @Test
    public void testExecuteOptionalWithRedisEnabled() {
        // 测试Redis启用时的executeOptional方法
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", true);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", unifiedRedisService);

        when(unifiedRedisService.getInfo("server")).thenReturn("redis_version:6.2.6");

        java.util.Optional<String> result = redisAvailabilityService.executeOptional(
            redis -> redis.getInfo("server")
        );

        assertTrue(result.isPresent());
        assertEquals("redis_version:6.2.6", result.get());
    }

    @Test
    public void testExecuteWithFallbackWithRedisEnabled() {
        // 测试Redis启用时的executeWithFallback方法
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", true);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", unifiedRedisService);

        when(unifiedRedisService.ping()).thenReturn("PONG");

        String result = redisAvailabilityService.executeWithFallback(
            redis -> redis.ping(),
            "default-value"
        );

        assertEquals("PONG", result);
    }

    @Test
    public void testExecuteWithFallbackOnException() {
        // 测试Redis操作抛出异常时的降级处理
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", true);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", unifiedRedisService);

        when(unifiedRedisService.ping()).thenThrow(new RuntimeException("Redis connection failed"));

        String result = redisAvailabilityService.executeWithFallback(
            redis -> redis.ping(),
            "fallback-value"
        );

        assertEquals("fallback-value", result);
    }

    @Test
    public void testRedisInfoWithException() {
        // 测试Redis信息获取时的异常处理
        ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", true);
        ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", unifiedRedisService);

        when(unifiedRedisService.getRedisInfo(any())).thenThrow(new RuntimeException("Redis info failed"));

        Map<String, String> result = redisAvailabilityService.getRedisInfo(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
