package com.xylink.manager.redis;

import com.xylink.manager.service.redis.RedisAvailabilityService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis弱关联配置集成测试
 * 
 * 测试在不同配置下Spring Boot应用的行为：
 * 1. Redis禁用时的Bean创建和服务行为
 * 2. Redis启用时的Bean创建和服务行为
 */
public class RedisWeakCouplingIntegrationTest {

    /**
     * 测试Redis禁用时的配置
     */
    @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
    @ActiveProfiles("test")
    @TestPropertySource(properties = {
        "redis.enabled=false",
        "spring.autoconfigure.exclude=" +
            "org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration," +
            "org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration," +
            "org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration"
    })
    static class RedisDisabledTest {

        @Autowired
        private ApplicationContext applicationContext;

        @Autowired
        private RedisAvailabilityService redisAvailabilityService;

        @Test
        public void testRedisDisabledConfiguration() {
            // 验证Redis相关Bean不应该被创建
            assertFalse(applicationContext.containsBean("unifiedRedisService"), 
                "UnifiedRedisService should not be created when redis.enabled=false");
            
            assertFalse(applicationContext.containsBean("jobRedisConfig"), 
                "JobRedisConfig should not be active when redis.enabled=false");
            
            // 验证RedisAvailabilityService正常创建
            assertNotNull(redisAvailabilityService, 
                "RedisAvailabilityService should always be available");
            
            // 验证Redis不可用
            assertFalse(redisAvailabilityService.isRedisAvailable(), 
                "Redis should not be available when redis.enabled=false");
        }

        @Test
        public void testRedisDisabledFallbackBehavior() {
            // 测试Redis禁用时的降级行为
            
            // 测试连接测试
            boolean connectionResult = redisAvailabilityService.testConnection("localhost", 6379, null, null);
            assertFalse(connectionResult, "Connection test should return false when Redis is disabled");
            
            // 测试获取Redis信息
            java.util.Map<String, String> redisInfo = redisAvailabilityService.getRedisInfo(null);
            assertNotNull(redisInfo, "Redis info should not be null");
            assertTrue(redisInfo.isEmpty(), "Redis info should be empty when Redis is disabled");

            // 测试主节点检查
            boolean isMaster = redisAvailabilityService.isRedisMaster("localhost", 6379, null, null);
            assertFalse(isMaster, "Master check should return false when Redis is disabled");

            // 测试内存信息
            java.util.Map<String, String> memoryInfo = redisAvailabilityService.getMemoryInfo();
            assertNotNull(memoryInfo, "Memory info should not be null");
            assertTrue(memoryInfo.isEmpty(), "Memory info should be empty when Redis is disabled");

            // 测试统计信息
            java.util.Map<String, String> statsInfo = redisAvailabilityService.getStatsInfo();
            assertNotNull(statsInfo, "Stats info should not be null");
            assertTrue(statsInfo.isEmpty(), "Stats info should be empty when Redis is disabled");
            
            // 测试PING命令
            String pingResult = redisAvailabilityService.ping();
            assertEquals("Redis不可用", pingResult, "Ping should return 'Redis不可用' when Redis is disabled");
            
            // 测试版本获取
            String version = redisAvailabilityService.getRedisVersion();
            assertEquals("unknown", version, "Version should return 'unknown' when Redis is disabled");
        }

        @Test
        public void testApplicationStartsWithoutRedis() {
            // 验证应用能够在没有Redis的情况下正常启动
            assertNotNull(applicationContext, "Application context should be available");
            
            // 验证关键服务仍然可用
            assertNotNull(redisAvailabilityService, "RedisAvailabilityService should be available");
            
            // 验证应用没有因为Redis不可用而启动失败
            String[] beanNames = applicationContext.getBeanDefinitionNames();
            assertTrue(beanNames.length > 0, "Application should have loaded beans successfully");
        }
    }

    /**
     * 测试Redis启用时的配置（模拟环境）
     */
    @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
    @TestPropertySource(properties = {
        "redis.enabled=true",
        "spring.redis.host=localhost",
        "spring.redis.port=6379",
        "spring.redis.timeout=2000ms",
        "spring.redis.database=0"
    })
    static class RedisEnabledTest {

        @Autowired
        private ApplicationContext applicationContext;

        @Autowired
        private RedisAvailabilityService redisAvailabilityService;

        @Test
        public void testRedisEnabledConfiguration() {
            // 验证RedisAvailabilityService正常创建
            assertNotNull(redisAvailabilityService, 
                "RedisAvailabilityService should be available");
            
            // 验证Redis被标记为启用
            assertTrue(redisAvailabilityService.isRedisAvailable(), 
                "Redis should be available when redis.enabled=true");
        }

        @Test
        public void testRedisEnabledBeanCreation() {
            // 注意：由于测试环境可能没有实际的Redis服务器，
            // 这里主要测试Bean的创建逻辑，而不是实际的Redis连接
            
            // 验证应用正常启动
            assertNotNull(applicationContext, "Application context should be available");
            
            // 验证RedisAvailabilityService可用
            assertNotNull(redisAvailabilityService, "RedisAvailabilityService should be available");
            
            // 验证Redis功能被启用
            assertTrue(redisAvailabilityService.isRedisAvailable(), 
                "Redis should be marked as available when enabled");
        }

        @Test
        public void testRedisEnabledFallbackOnConnectionFailure() {
            // 测试Redis启用但连接失败时的降级行为
            // 由于测试环境可能没有Redis服务器，连接会失败，但应该优雅降级
            
            // 测试连接（预期失败但不抛异常）
            boolean connectionResult = redisAvailabilityService.testConnection("localhost", 6379, null, null);
            // 不强制要求连接成功，因为测试环境可能没有Redis
            
            // 测试PING（应该有降级处理）
            String pingResult = redisAvailabilityService.ping();
            assertNotNull(pingResult, "Ping should return a result (either PONG or fallback)");
            
            // 测试版本获取（应该有降级处理）
            String version = redisAvailabilityService.getRedisVersion();
            assertNotNull(version, "Version should return a result (either actual version or fallback)");
        }
    }
}
