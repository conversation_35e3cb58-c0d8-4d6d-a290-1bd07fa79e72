package com.xylink.manager.redis;

import com.xylink.manager.service.redis.RedisAvailabilityService;
import com.xylink.manager.service.redis.UnifiedRedisService;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis启用Docker测试
 * 
 * 使用Docker Redis实例测试Redis启用时的功能
 * 
 * 前置条件：需要启动Docker Redis容器
 * docker run --name redis-test -p 6379:6379 -d redis:7-alpine
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(properties = {
    "redis.enabled=true",
    "spring.redis.host=localhost",
    "spring.redis.port=6379",
    "spring.redis.timeout=2000ms",
    "spring.redis.database=0",
    "third.k8s.switch=true",  // 禁用K8s功能
    "base.dir=./",
    "k8s.config.path=./config_56_thirddev"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("Redis启用Docker测试")
public class RedisEnabledDockerTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private RedisAvailabilityService redisAvailabilityService;

    @Autowired(required = false)
    private UnifiedRedisService unifiedRedisService;

    @BeforeAll
    static void setup() {
        System.out.println("=== Redis启用测试开始 ===");
        System.out.println("确保Docker Redis容器正在运行：docker run --name redis-test -p 6379:6379 -d redis:7-alpine");
    }

    @Test
    @Order(1)
    @DisplayName("验证Redis启用时Bean被正确创建")
    public void testRedisEnabledBeanCreation() {
        // 验证RedisAvailabilityService正常创建
        assertNotNull(redisAvailabilityService, 
            "RedisAvailabilityService should be available");
        
        // 验证UnifiedRedisService被创建
        assertNotNull(unifiedRedisService, 
            "UnifiedRedisService should be created when redis.enabled=true");
        
        // 验证Redis被标记为启用
        assertTrue(redisAvailabilityService.isRedisAvailable(), 
            "Redis should be available when redis.enabled=true");
        
        System.out.println("✅ Redis Bean创建验证通过");
    }

    @Test
    @Order(2)
    @DisplayName("验证Redis连接功能")
    public void testRedisConnection() {
        // 测试连接
        boolean connectionResult = redisAvailabilityService.testConnection("localhost", 6379, null, null);
        assertTrue(connectionResult, "Should be able to connect to Docker Redis instance");
        
        // 测试PING命令
        String pingResult = redisAvailabilityService.ping();
        assertEquals("PONG", pingResult, "Ping should return 'PONG' when Redis is available");
        
        System.out.println("✅ Redis连接测试通过");
        System.out.println("   - 连接测试: " + connectionResult);
        System.out.println("   - PING结果: " + pingResult);
    }

    @Test
    @Order(3)
    @DisplayName("验证Redis信息获取功能")
    public void testRedisInfo() {
        // 测试版本获取
        String version = redisAvailabilityService.getRedisVersion();
        assertNotNull(version, "Version should not be null");
        assertNotEquals("unknown", version, "Version should not be 'unknown' when Redis is available");
        assertTrue(version.contains("."), "Version should contain version numbers");
        
        // 测试获取Redis信息
        Map<String, String> redisInfo = redisAvailabilityService.getRedisInfo("server");
        assertNotNull(redisInfo, "Redis info should not be null");
        
        System.out.println("✅ Redis信息获取测试通过");
        System.out.println("   - Redis版本: " + version);
        System.out.println("   - 信息字段数: " + redisInfo.size());
        
        // 打印一些关键信息
        if (!redisInfo.isEmpty()) {
            redisInfo.entrySet().stream()
                .filter(entry -> entry.getKey().contains("redis_version") || 
                               entry.getValue().contains("redis_version"))
                .limit(3)
                .forEach(entry -> System.out.println("   - " + entry.getKey() + ": " + entry.getValue()));
        }
    }

    @Test
    @Order(4)
    @DisplayName("验证Redis内存和统计信息")
    public void testRedisMemoryAndStats() {
        // 测试内存信息
        Map<String, String> memoryInfo = redisAvailabilityService.getMemoryInfo();
        assertNotNull(memoryInfo, "Memory info should not be null");
        
        // 测试统计信息
        Map<String, String> statsInfo = redisAvailabilityService.getStatsInfo();
        assertNotNull(statsInfo, "Stats info should not be null");
        
        System.out.println("✅ Redis内存和统计信息测试通过");
        System.out.println("   - 内存信息字段数: " + memoryInfo.size());
        System.out.println("   - 统计信息字段数: " + statsInfo.size());
        
        // 打印一些内存信息
        if (!memoryInfo.isEmpty()) {
            memoryInfo.entrySet().stream()
                .filter(entry -> entry.getKey().contains("memory") || 
                               entry.getValue().contains("memory"))
                .limit(2)
                .forEach(entry -> System.out.println("   - " + entry.getKey() + ": " + entry.getValue()));
        }
    }

    @Test
    @Order(5)
    @DisplayName("验证Redis主节点检查")
    public void testRedisMasterCheck() {
        // 测试主节点检查（单机Redis应该返回true）
        boolean isMaster = redisAvailabilityService.isRedisMaster("localhost", 6379, null, null);
        assertTrue(isMaster, "Single Redis instance should be considered as master");
        
        System.out.println("✅ Redis主节点检查测试通过");
        System.out.println("   - 是否为主节点: " + isMaster);
    }

    @Test
    @Order(6)
    @DisplayName("验证Redis弱关联功能")
    public void testRedisWeakCoupling() {
        // 测试executeWithFallback方法
        String result1 = redisAvailabilityService.executeWithFallback(
            redis -> redis.ping(),
            "fallback-value"
        );
        assertEquals("PONG", result1, "Should return actual Redis result when available");
        
        // 测试executeOptional方法
        java.util.Optional<String> result2 = redisAvailabilityService.executeOptional(
            redis -> redis.ping()
        );
        assertTrue(result2.isPresent(), "Should return present Optional when Redis is available");
        assertEquals("PONG", result2.get(), "Should return actual Redis result");
        
        System.out.println("✅ Redis弱关联功能测试通过");
        System.out.println("   - executeWithFallback结果: " + result1);
        System.out.println("   - executeOptional结果: " + result2.orElse("empty"));
    }

    @AfterAll
    static void teardown() {
        System.out.println("=== Redis启用测试结束 ===");
        System.out.println("注意：Redis容器将在所有测试完成后自动清理");
    }
}
