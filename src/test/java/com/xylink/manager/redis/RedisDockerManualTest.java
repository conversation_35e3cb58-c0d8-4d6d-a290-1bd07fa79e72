package com.xylink.manager.redis;

import com.xylink.manager.service.redis.RedisAvailabilityService;
import com.xylink.manager.service.redis.UnifiedRedisService;
import org.junit.jupiter.api.*;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis Docker手动测试
 * 
 * 不依赖Spring Boot上下文，直接测试Redis弱关联功能
 * 
 * 前置条件：需要启动Docker Redis容器
 * docker run --name redis-test -p 6379:6379 -d redis:7-alpine
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("Redis Docker手动测试")
public class RedisDockerManualTest {

    private RedisAvailabilityService redisAvailabilityService;
    private UnifiedRedisService unifiedRedisService;
    private RedissonClient redissonClient;

    @BeforeAll
    static void setup() {
        System.out.println("=== Redis Docker手动测试开始 ===");
        System.out.println("确保Docker Redis容器正在运行：docker run --name redis-test -p 6379:6379 -d redis:7-alpine");
    }

    @BeforeEach
    void setupEach() {
        // 手动创建Redis服务
        redisAvailabilityService = new RedisAvailabilityService();
        
        try {
            // 创建Redisson客户端连接到Docker Redis
            Config config = new Config();
            config.useSingleServer()
                .setAddress("redis://localhost:6379")
                .setConnectionMinimumIdleSize(1)
                .setConnectionPoolSize(5)
                .setConnectTimeout(2000)
                .setTimeout(2000);
            
            redissonClient = Redisson.create(config);
            
            // 创建UnifiedRedisService
            unifiedRedisService = new UnifiedRedisService();
            ReflectionTestUtils.setField(unifiedRedisService, "redissonClient", redissonClient);
            
            // 设置RedisAvailabilityService的依赖
            ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", true);
            ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", unifiedRedisService);
            
            System.out.println("✅ Redis服务初始化完成");
        } catch (Exception e) {
            System.err.println("❌ Redis服务初始化失败: " + e.getMessage());
            // 设置为禁用状态
            ReflectionTestUtils.setField(redisAvailabilityService, "redisEnabled", false);
            ReflectionTestUtils.setField(redisAvailabilityService, "unifiedRedisService", null);
        }
    }

    @AfterEach
    void teardownEach() {
        if (redissonClient != null && !redissonClient.isShutdown()) {
            redissonClient.shutdown();
        }
    }

    @Test
    @Order(1)
    @DisplayName("验证Redis可用性检查")
    public void testRedisAvailability() {
        boolean isAvailable = redisAvailabilityService.isRedisAvailable();
        System.out.println("Redis可用性: " + isAvailable);
        
        if (isAvailable) {
            assertTrue(isAvailable, "Redis should be available when properly configured");
        } else {
            assertFalse(isAvailable, "Redis should not be available when connection fails");
        }
    }

    @Test
    @Order(2)
    @DisplayName("验证Redis连接测试")
    public void testRedisConnection() {
        boolean connectionResult = redisAvailabilityService.testConnection("localhost", 6379, null, null);
        System.out.println("连接测试结果: " + connectionResult);
        
        // 不强制要求连接成功，因为可能没有Redis服务器
        // 但应该不抛出异常
        assertNotNull(connectionResult);
    }

    @Test
    @Order(3)
    @DisplayName("验证Redis PING命令")
    public void testRedisPing() {
        String pingResult = redisAvailabilityService.ping();
        System.out.println("PING结果: " + pingResult);
        
        assertNotNull(pingResult, "Ping result should not be null");
        
        if (redisAvailabilityService.isRedisAvailable()) {
            assertEquals("PONG", pingResult, "Should return PONG when Redis is available");
        } else {
            assertEquals("Redis不可用", pingResult, "Should return fallback message when Redis is not available");
        }
    }

    @Test
    @Order(4)
    @DisplayName("验证Redis版本获取")
    public void testRedisVersion() {
        String version = redisAvailabilityService.getRedisVersion();
        System.out.println("Redis版本: " + version);
        
        assertNotNull(version, "Version should not be null");
        
        if (redisAvailabilityService.isRedisAvailable()) {
            assertNotEquals("unknown", version, "Should return actual version when Redis is available");
            assertTrue(version.contains("."), "Version should contain version numbers");
        } else {
            assertEquals("unknown", version, "Should return 'unknown' when Redis is not available");
        }
    }

    @Test
    @Order(5)
    @DisplayName("验证Redis信息获取")
    public void testRedisInfo() {
        Map<String, String> redisInfo = redisAvailabilityService.getRedisInfo("server");
        System.out.println("Redis信息字段数: " + redisInfo.size());
        
        assertNotNull(redisInfo, "Redis info should not be null");
        
        if (redisAvailabilityService.isRedisAvailable() && !redisInfo.isEmpty()) {
            System.out.println("Redis信息示例:");
            redisInfo.entrySet().stream()
                .limit(3)
                .forEach(entry -> System.out.println("  " + entry.getKey() + ": " + entry.getValue()));
        } else {
            System.out.println("Redis信息为空（降级处理）");
        }
    }

    @Test
    @Order(6)
    @DisplayName("验证Redis主节点检查")
    public void testRedisMasterCheck() {
        boolean isMaster = redisAvailabilityService.isRedisMaster("localhost", 6379, null, null);
        System.out.println("是否为主节点: " + isMaster);
        
        if (redisAvailabilityService.isRedisAvailable()) {
            assertTrue(isMaster, "Single Redis instance should be considered as master");
        } else {
            assertFalse(isMaster, "Should return false when Redis is not available");
        }
    }

    @Test
    @Order(7)
    @DisplayName("验证Redis弱关联功能")
    public void testRedisWeakCoupling() {
        // 测试executeWithFallback方法
        String result1 = redisAvailabilityService.executeWithFallback(
            redis -> redis.ping(),
            "fallback-value"
        );
        System.out.println("executeWithFallback结果: " + result1);
        
        assertNotNull(result1, "Result should not be null");
        
        if (redisAvailabilityService.isRedisAvailable()) {
            assertEquals("PONG", result1, "Should return actual Redis result when available");
        } else {
            assertEquals("fallback-value", result1, "Should return fallback value when Redis is not available");
        }
        
        // 测试executeOptional方法
        java.util.Optional<String> result2 = redisAvailabilityService.executeOptional(
            redis -> redis.ping()
        );
        System.out.println("executeOptional结果: " + result2.orElse("empty"));
        
        assertNotNull(result2, "Optional result should not be null");
        
        if (redisAvailabilityService.isRedisAvailable()) {
            assertTrue(result2.isPresent(), "Should return present Optional when Redis is available");
            assertEquals("PONG", result2.get(), "Should return actual Redis result");
        } else {
            assertFalse(result2.isPresent(), "Should return empty Optional when Redis is not available");
        }
    }

    @Test
    @Order(8)
    @DisplayName("验证Redis内存和统计信息")
    public void testRedisMemoryAndStats() {
        // 测试内存信息
        Map<String, String> memoryInfo = redisAvailabilityService.getMemoryInfo();
        System.out.println("内存信息字段数: " + memoryInfo.size());
        
        assertNotNull(memoryInfo, "Memory info should not be null");
        
        // 测试统计信息
        Map<String, String> statsInfo = redisAvailabilityService.getStatsInfo();
        System.out.println("统计信息字段数: " + statsInfo.size());
        
        assertNotNull(statsInfo, "Stats info should not be null");
        
        if (redisAvailabilityService.isRedisAvailable()) {
            // 如果Redis可用，可能会有一些信息
            System.out.println("Redis可用，内存和统计信息可能包含数据");
        } else {
            // 如果Redis不可用，应该返回空Map
            assertTrue(memoryInfo.isEmpty(), "Memory info should be empty when Redis is not available");
            assertTrue(statsInfo.isEmpty(), "Stats info should be empty when Redis is not available");
        }
    }

    @AfterAll
    static void teardown() {
        System.out.println("=== Redis Docker手动测试结束 ===");
        System.out.println("注意：如果需要清理Redis容器，请运行：");
        System.out.println("docker stop redis-test && docker rm redis-test");
    }
}
