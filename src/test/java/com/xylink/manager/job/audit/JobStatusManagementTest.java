package com.xylink.manager.job.audit;

import com.xylink.manager.job.model.*;
import com.xylink.manager.job.service.InMemoryJobStatusService;
import com.xylink.manager.service.redis.RedisAvailabilityService;
import org.junit.jupiter.api.*;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Job状态管理测试
 * 
 * 测试目标：
 * 1. 验证Job状态存储功能
 * 2. 验证Job状态查询功能
 * 3. 验证Job状态更新功能
 * 4. 验证Redis启用/禁用时的不同表现
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("Job状态管理测试")
public class JobStatusManagementTest {

    private InMemoryJobStatusService inMemoryJobStatusService;
    
    @Mock
    private RedisAvailabilityService redisAvailabilityService;

    @BeforeAll
    static void setupClass() {
        System.out.println("=== Job状态管理测试开始 ===");
    }

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        inMemoryJobStatusService = new InMemoryJobStatusService();
    }

    @Test
    @Order(1)
    @DisplayName("1. Job状态存储测试")
    public void testJobStatusStorage() {
        String jobName = "test-status-job";
        String executionId = "exec-001";
        
        // 测试初始状态
        JobStatus initialStatus = inMemoryJobStatusService.getJobStatus(jobName);
        assertNotNull(initialStatus, "初始状态不应为null");
        assertEquals(jobName, initialStatus.getJobName(), "Job名称应该正确");
        assertFalse(initialStatus.isRunning(), "初始状态应该不是运行中");
        assertNull(initialStatus.getLastTriggerTime(), "初始状态没有触发时间");

        // 测试记录Job开始
        inMemoryJobStatusService.recordJobStart(jobName, executionId);

        JobStatus runningStatus = inMemoryJobStatusService.getJobStatus(jobName);
        assertTrue(runningStatus.isRunning(), "Job应该处于运行状态");
        assertEquals(executionId, runningStatus.getLastExecutionId(), "执行ID应该正确");
        assertNotNull(runningStatus.getLastTriggerTime(), "应该记录执行开始时间");

        // 测试记录Job完成
        JobExecutionResult result = JobExecutionResult.success(1500L, "测试成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);

        JobStatus completedStatus = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(completedStatus.isRunning(), "Job应该不再运行");
        assertNotNull(completedStatus.getLastFinishTime(), "应该保留最后完成时间");
        
        System.out.println("✅ Job状态存储测试通过");
        System.out.println("   - 初始状态: ✅");
        System.out.println("   - 运行状态: ✅");
        System.out.println("   - 完成状态: ✅");
    }

    @Test
    @Order(2)
    @DisplayName("2. Job状态查询测试")
    public void testJobStatusQuery() {
        String jobName = "query-test-job";
        
        // 测试查询不存在的Job
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        assertNotNull(status, "不存在的Job应该返回默认状态");
        assertEquals(jobName, status.getJobName(), "Job名称应该正确");
        
        // 创建Job状态后查询
        String executionId = "exec-query-001";
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        
        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertNotNull(status, "状态不应为null");
        assertTrue(status.isRunning(), "Job应该处于运行状态");
        
        // 测试查询所有Job状态
        List<JobStatus> allStatuses = inMemoryJobStatusService.getAllJobStatus();
        assertNotNull(allStatuses, "所有状态列表不应为null");
        assertTrue(allStatuses.size() > 0, "应该有Job状态记录");

        boolean found = allStatuses.stream()
            .anyMatch(s -> jobName.equals(s.getJobName()));
        assertTrue(found, "应该能找到测试Job的状态");

        System.out.println("✅ Job状态查询测试通过");
        System.out.println("   - 单个状态查询: ✅");
        System.out.println("   - 所有状态查询: ✅");
        System.out.println("   - 查询结果数量: " + allStatuses.size());
    }

    @Test
    @Order(3)
    @DisplayName("3. Job执行历史测试")
    public void testJobExecutionHistory() {
        String jobName = "history-test-job";
        
        // 执行多次Job
        for (int i = 1; i <= 3; i++) {
            String executionId = "exec-history-" + String.format("%03d", i);
            
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            
            // 模拟不同的执行结果
            JobExecutionResult result;
            if (i == 2) {
                result = JobExecutionResult.failure(2000L, "测试失败", "错误信息", 1);
            } else {
                result = JobExecutionResult.success(1000L + i * 100, "测试成功 " + i);
            }

            inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
        }

        // 查询执行历史
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertNotNull(history, "执行历史不应为null");
        assertEquals(3, history.size(), "应该有3条执行历史");

        // 验证历史记录顺序（最新的在前）
        JobExecutionHistory latest = history.get(0);
        assertEquals("exec-history-003", latest.getExecutionId(), "最新记录的执行ID应该正确");
        assertEquals("SUCCESS", latest.getStatus(), "最新记录应该是成功的");

        // 验证失败记录
        JobExecutionHistory failedRecord = history.stream()
            .filter(h -> "FAIL".equals(h.getStatus()))
            .findFirst()
            .orElse(null);
        assertNotNull(failedRecord, "应该有失败记录");
        assertEquals("exec-history-002", failedRecord.getExecutionId(), "失败记录的执行ID应该正确");

        // 测试限制数量
        List<JobExecutionHistory> limitedHistory = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 2);
        assertEquals(2, limitedHistory.size(), "限制数量应该生效");

        System.out.println("✅ Job执行历史测试通过");
        System.out.println("   - 历史记录数量: " + history.size());
        System.out.println("   - 成功记录: " + history.stream().mapToInt(h -> "SUCCESS".equals(h.getStatus()) ? 1 : 0).sum());
        System.out.println("   - 失败记录: " + history.stream().mapToInt(h -> "FAIL".equals(h.getStatus()) ? 1 : 0).sum());
    }

    @Test
    @Order(4)
    @DisplayName("4. Redis启用时的状态管理测试")
    public void testStatusManagementWithRedisEnabled() {
        // 模拟Redis可用的情况
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(true);
        when(redisAvailabilityService.executeWithFallback(any(), any())).thenAnswer(invocation -> {
            // 模拟Redis操作成功
            return "Redis操作成功";
        });
        
        String jobName = "redis-enabled-job";
        String executionId = "exec-redis-001";
        
        // 在Redis可用时，状态管理应该正常工作
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        
        assertNotNull(status, "状态不应为null");
        assertTrue(status.isRunning(), "Job应该处于运行状态");
        
        // 完成Job
        JobExecutionResult result = JobExecutionResult.success(1200L, "Redis模式成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);

        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "Job应该完成");

        System.out.println("✅ Redis启用时的状态管理测试通过");
        System.out.println("   - Redis可用性检查: ✅");
        System.out.println("   - 状态记录: ✅");
    }

    @Test
    @Order(5)
    @DisplayName("5. Redis禁用时的状态管理测试")
    public void testStatusManagementWithRedisDisabled() {
        // 模拟Redis不可用的情况
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(false);
        when(redisAvailabilityService.executeWithFallback(any(), any())).thenAnswer(invocation -> {
            // 返回默认值（降级处理）
            return invocation.getArgument(1);
        });
        
        String jobName = "redis-disabled-job";
        String executionId = "exec-memory-001";
        
        // 在Redis不可用时，应该使用内存模式
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        
        assertNotNull(status, "内存模式下状态不应为null");
        assertTrue(status.isRunning(), "内存模式下Job应该处于运行状态");
        
        // 完成Job
        JobExecutionResult result = JobExecutionResult.success(800L, "内存模式成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);

        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "内存模式下Job应该完成");

        // 验证执行历史在内存模式下也正常工作
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertNotNull(history, "内存模式下执行历史不应为null");
        assertEquals(1, history.size(), "应该有1条执行历史");

        System.out.println("✅ Redis禁用时的状态管理测试通过");
        System.out.println("   - 内存模式状态管理: ✅");
        System.out.println("   - 内存模式执行历史: ✅");
    }

    @Test
    @Order(6)
    @DisplayName("6. 状态管理异常处理测试")
    public void testStatusManagementErrorHandling() {
        String jobName = "error-test-job";
        String executionId = "exec-error-001";
        
        // 测试异常情况下的状态管理
        try {
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            
            // 模拟Job执行失败
            JobExecutionResult errorResult = JobExecutionResult.failure(500L, "执行失败", "错误详情", 1);
            inMemoryJobStatusService.recordJobCompletion(jobName, executionId, errorResult);
            
            JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
            assertFalse(status.isRunning(), "失败的Job应该不再运行");
            
            // 验证失败记录
            List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
            assertEquals(1, history.size(), "应该有1条失败记录");
            assertEquals("FAIL", history.get(0).getStatus(), "记录应该标记为失败");
            
        } catch (Exception e) {
            fail("状态管理不应该抛出异常: " + e.getMessage());
        }
        
        // 测试空值处理
        assertDoesNotThrow(() -> {
            inMemoryJobStatusService.recordJobStart(null, executionId);
            inMemoryJobStatusService.recordJobCompletion(jobName, null, null);
        }, "空值处理不应该抛出异常");
        
        System.out.println("✅ 状态管理异常处理测试通过");
        System.out.println("   - 失败状态处理: ✅");
        System.out.println("   - 空值处理: ✅");
    }

    @AfterAll
    static void teardownClass() {
        System.out.println("=== Job状态管理测试结束 ===");
    }
}
