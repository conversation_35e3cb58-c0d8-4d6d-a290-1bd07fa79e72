package com.xylink.manager.job.audit;

import com.xylink.manager.job.model.JobConfig;
import com.xylink.manager.job.lock.InMemoryJobLock;
import com.xylink.manager.job.model.*;
import com.xylink.manager.job.service.InMemoryJobStatusService;
import com.xylink.manager.service.redis.RedisAvailabilityService;
import org.junit.jupiter.api.*;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Job集成测试
 * 
 * 测试目标：
 * 1. 端到端测试Job系统的完整工作流程
 * 2. 验证配置加载、调度、执行、监控的完整链路
 * 3. 验证Redis启用/禁用时的完整功能
 * 4. 验证异常情况下的系统行为
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("Job集成测试")
public class JobIntegrationTest {

    private InMemoryJobLock inMemoryJobLock;
    private InMemoryJobStatusService inMemoryJobStatusService;
    
    @Mock
    private RedisAvailabilityService redisAvailabilityService;

    @BeforeAll
    static void setupClass() {
        System.out.println("=== Job集成测试开始 ===");
    }

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        inMemoryJobLock = new InMemoryJobLock();
        inMemoryJobStatusService = new InMemoryJobStatusService();
    }

    @AfterEach
    void cleanup() {
        inMemoryJobLock.clearAllLocks();
    }

    @Test
    @Order(1)
    @DisplayName("1. 完整Job工作流程测试（Redis启用）")
    public void testCompleteJobWorkflowWithRedis() throws InterruptedException {
        // 模拟Redis可用
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(true);
        when(redisAvailabilityService.executeWithFallback(any(), any())).thenAnswer(invocation -> {
            return "Redis操作成功";
        });
        
        String jobName = "integration-redis-job";
        String executionId = "exec-integration-001";
        
        // 阶段1：配置加载
        JobConfig jobConfig = createTestJobConfig(jobName);
        assertNotNull(jobConfig, "Job配置应该创建成功");
        assertEquals(jobName, jobConfig.getJobName(), "Job名称应该正确");
        assertTrue(jobConfig.isEnabled(), "Job应该启用");
        
        // 阶段2：获取分布式锁（模拟）
        assertTrue(inMemoryJobLock.tryLock(jobName), "应该能获取锁");
        assertTrue(inMemoryJobLock.isLocked(jobName), "锁应该被持有");
        
        // 阶段3：记录Job开始执行
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        
        assertTrue(status.isRunning(), "Job应该处于运行状态");
        assertEquals(jobName, status.getJobName(), "Job名称应该正确");
        assertNotNull(status.getLastTriggerTime(), "应该记录触发时间");
        
        // 阶段4：模拟Job执行
        Thread.sleep(100); // 模拟执行时间
        
        // 阶段5：记录Job完成
        JobExecutionResult result = JobExecutionResult.success(1500L, "集成测试成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
        
        // 阶段6：验证执行结果
        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "Job应该完成");
        assertEquals("SUCCESS", status.getLastStatus(), "状态应该为成功");
        assertNotNull(status.getLastFinishTime(), "应该记录完成时间");
        
        // 阶段7：释放锁
        inMemoryJobLock.releaseLock(jobName);
        assertFalse(inMemoryJobLock.isLocked(jobName), "锁应该被释放");
        
        // 阶段8：验证执行历史
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertEquals(1, history.size(), "应该有1条执行历史");
        assertEquals("SUCCESS", history.get(0).getStatus(), "历史记录应该为成功");
        
        System.out.println("✅ 完整Job工作流程测试（Redis启用）通过");
        System.out.println("   - 配置加载: ✅");
        System.out.println("   - 锁获取: ✅");
        System.out.println("   - 状态管理: ✅");
        System.out.println("   - 执行历史: ✅");
    }

    @Test
    @Order(2)
    @DisplayName("2. 完整Job工作流程测试（Redis禁用）")
    public void testCompleteJobWorkflowWithoutRedis() throws InterruptedException {
        // 模拟Redis不可用
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(false);
        when(redisAvailabilityService.executeWithFallback(any(), any())).thenAnswer(invocation -> {
            return invocation.getArgument(1); // 返回降级值
        });
        
        String jobName = "integration-memory-job";
        String executionId = "exec-integration-002";
        
        // 阶段1：配置加载（不依赖Redis）
        JobConfig jobConfig = createTestJobConfig(jobName);
        assertNotNull(jobConfig, "Job配置应该创建成功");
        
        // 阶段2：获取内存锁（降级模式）
        assertTrue(inMemoryJobLock.tryLock(jobName), "降级模式下应该能获取内存锁");
        
        // 阶段3：记录Job开始执行（内存模式）
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        
        assertTrue(status.isRunning(), "降级模式下Job应该运行");
        
        // 阶段4：模拟Job执行
        Thread.sleep(50);
        
        // 阶段5：记录Job完成（内存模式）
        JobExecutionResult result = JobExecutionResult.success(800L, "降级模式成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
        
        // 阶段6：验证执行结果
        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "Job应该完成");
        assertEquals("SUCCESS", status.getLastStatus(), "状态应该为成功");
        
        // 阶段7：释放锁
        inMemoryJobLock.releaseLock(jobName);
        assertFalse(inMemoryJobLock.isLocked(jobName), "锁应该被释放");
        
        // 阶段8：验证执行历史（内存存储）
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertEquals(1, history.size(), "应该有1条执行历史");
        assertEquals("SUCCESS", history.get(0).getStatus(), "历史记录应该为成功");
        
        System.out.println("✅ 完整Job工作流程测试（Redis禁用）通过");
        System.out.println("   - 降级模式: ✅");
        System.out.println("   - 内存存储: ✅");
        System.out.println("   - 完整流程: ✅");
    }

    @Test
    @Order(3)
    @DisplayName("3. 多Job并发执行集成测试")
    public void testMultipleJobsConcurrentExecution() throws InterruptedException {
        // 模拟Redis可用
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(true);
        
        String[] jobNames = {"job-1", "job-2", "job-3"};
        
        // 并发执行多个Job
        Thread[] threads = new Thread[jobNames.length];
        
        for (int i = 0; i < jobNames.length; i++) {
            final String jobName = jobNames[i];
            final int jobIndex = i + 1;
            
            threads[i] = new Thread(() -> {
                try {
                    String executionId = "exec-concurrent-" + jobIndex;
                    
                    // 获取锁
                    assertTrue(inMemoryJobLock.tryLock(jobName), "应该能获取锁: " + jobName);
                    
                    // 记录开始
                    inMemoryJobStatusService.recordJobStart(jobName, executionId);
                    
                    // 模拟执行
                    Thread.sleep(100 + jobIndex * 50);
                    
                    // 记录完成
                    JobExecutionResult result = JobExecutionResult.success(
                        (long)(100 + jobIndex * 50), 
                        "并发测试成功 " + jobIndex
                    );
                    inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
                    
                    // 释放锁
                    inMemoryJobLock.releaseLock(jobName);
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    fail("Job执行被中断: " + jobName);
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join(5000); // 最多等待5秒
        }
        
        // 验证所有Job都完成了
        for (String jobName : jobNames) {
            JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
            assertFalse(status.isRunning(), "Job应该完成: " + jobName);
            assertEquals("SUCCESS", status.getLastStatus(), "状态应该为成功: " + jobName);
            assertFalse(inMemoryJobLock.isLocked(jobName), "锁应该被释放: " + jobName);
            
            // 验证执行历史
            List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
            assertEquals(1, history.size(), "应该有1条执行历史: " + jobName);
        }
        
        System.out.println("✅ 多Job并发执行集成测试通过");
        System.out.println("   - 并发Job数: " + jobNames.length);
        System.out.println("   - 全部成功完成: ✅");
    }

    @Test
    @Order(4)
    @DisplayName("4. Job异常处理集成测试")
    public void testJobErrorHandlingIntegration() {
        String jobName = "error-integration-job";
        String executionId = "exec-error-integration-001";
        
        // 模拟Redis可用
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(true);
        
        try {
            // 获取锁
            assertTrue(inMemoryJobLock.tryLock(jobName), "应该能获取锁");
            
            // 记录开始
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            
            // 模拟Job执行异常
            throw new RuntimeException("模拟Job执行异常");
            
        } catch (Exception e) {
            // 异常处理流程
            
            // 1. 强制释放锁
            inMemoryJobLock.forceReleaseLock(jobName);
            
            // 2. 记录失败状态
            JobExecutionResult errorResult = JobExecutionResult.failure(
                300L, 
                "Job执行异常", 
                e.getMessage(), 
                0
            );
            inMemoryJobStatusService.recordJobCompletion(jobName, executionId, errorResult);
        }
        
        // 验证异常处理结果
        assertFalse(inMemoryJobLock.isLocked(jobName), "异常后锁应该被释放");
        
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "异常后Job不应该运行");
        assertEquals("FAIL", status.getLastStatus(), "状态应该为失败");
        
        // 验证执行历史
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertEquals(1, history.size(), "应该有1条执行历史");
        assertEquals("FAIL", history.get(0).getStatus(), "历史记录应该为失败");
        
        // 验证系统恢复能力
        assertTrue(inMemoryJobLock.tryLock(jobName), "异常处理后应该能重新获取锁");
        inMemoryJobLock.releaseLock(jobName);
        
        System.out.println("✅ Job异常处理集成测试通过");
        System.out.println("   - 异常捕获: ✅");
        System.out.println("   - 锁释放: ✅");
        System.out.println("   - 状态记录: ✅");
        System.out.println("   - 系统恢复: ✅");
    }

    @Test
    @Order(5)
    @DisplayName("5. Redis切换场景集成测试")
    public void testRedisFailoverIntegration() {
        String jobName = "failover-integration-job";
        
        // 场景1：Redis可用时执行Job
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(true);
        
        String executionId1 = "exec-failover-001";
        assertTrue(inMemoryJobLock.tryLock(jobName), "Redis模式下应该能获取锁");
        inMemoryJobStatusService.recordJobStart(jobName, executionId1);
        
        JobExecutionResult result1 = JobExecutionResult.success(1000L, "Redis模式成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId1, result1);
        inMemoryJobLock.releaseLock(jobName);
        
        // 场景2：Redis故障，切换到降级模式
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(false);
        
        String executionId2 = "exec-failover-002";
        assertTrue(inMemoryJobLock.tryLock(jobName), "降级模式下应该能获取锁");
        inMemoryJobStatusService.recordJobStart(jobName, executionId2);
        
        JobExecutionResult result2 = JobExecutionResult.success(800L, "降级模式成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId2, result2);
        inMemoryJobLock.releaseLock(jobName);
        
        // 场景3：Redis恢复
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(true);
        
        String executionId3 = "exec-failover-003";
        assertTrue(inMemoryJobLock.tryLock(jobName), "Redis恢复后应该能获取锁");
        inMemoryJobStatusService.recordJobStart(jobName, executionId3);
        
        JobExecutionResult result3 = JobExecutionResult.success(1200L, "Redis恢复成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId3, result3);
        inMemoryJobLock.releaseLock(jobName);
        
        // 验证整个切换过程
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertEquals(3, history.size(), "应该有3条执行历史");
        
        // 验证所有执行都成功
        long successCount = history.stream().filter(h -> "SUCCESS".equals(h.getStatus())).count();
        assertEquals(3, successCount, "所有执行都应该成功");
        
        // 验证最终状态
        JobStatus finalStatus = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(finalStatus.isRunning(), "最终Job不应该运行");
        assertEquals("SUCCESS", finalStatus.getLastStatus(), "最终状态应该为成功");
        
        System.out.println("✅ Redis切换场景集成测试通过");
        System.out.println("   - Redis模式执行: ✅");
        System.out.println("   - 降级模式执行: ✅");
        System.out.println("   - Redis恢复执行: ✅");
        System.out.println("   - 总执行次数: " + history.size());
        System.out.println("   - 成功次数: " + successCount);
    }

    /**
     * 创建测试用的Job配置
     */
    private JobConfig createTestJobConfig(String jobName) {
        JobConfig config = new JobConfig();
        config.setJobName(jobName);
        config.setType(JobType.API);
        config.setEnabled(true);
        config.setCron("0 */5 * * * ?"); // 每5分钟执行一次
        config.setDescription("集成测试Job: " + jobName);
        config.setRetryCount(3);
        return config;
    }

    @AfterAll
    static void teardownClass() {
        System.out.println("=== Job集成测试结束 ===");
    }
}
