package com.xylink.manager.job.audit;

import com.xylink.manager.job.lock.InMemoryJobLock;
import com.xylink.manager.job.service.InMemoryJobStatusService;
import com.xylink.manager.job.model.*;
import com.xylink.manager.service.redis.RedisAvailabilityService;
import org.junit.jupiter.api.*;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Job降级功能测试
 * 
 * 测试目标：
 * 1. 验证Redis不可用时的降级处理
 * 2. 验证内存存储功能
 * 3. 验证降级模式下的完整工作流程
 * 4. 验证Redis恢复后的切换机制
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("Job降级功能测试")
public class JobDegradationTest {

    private InMemoryJobLock inMemoryJobLock;
    private InMemoryJobStatusService inMemoryJobStatusService;
    
    @Mock
    private RedisAvailabilityService redisAvailabilityService;

    @BeforeAll
    static void setupClass() {
        System.out.println("=== Job降级功能测试开始 ===");
    }

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        inMemoryJobLock = new InMemoryJobLock();
        inMemoryJobStatusService = new InMemoryJobStatusService();
    }

    @AfterEach
    void cleanup() {
        inMemoryJobLock.clearAllLocks();
    }

    @Test
    @Order(1)
    @DisplayName("1. Redis不可用时的降级处理测试")
    public void testRedisUnavailableDegradation() {
        // 模拟Redis不可用
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(false);
        when(redisAvailabilityService.executeWithFallback(any(), any())).thenAnswer(invocation -> {
            // 返回降级值
            return invocation.getArgument(1);
        });
        
        String jobName = "degradation-test-job";
        String executionId = "exec-degradation-001";
        
        // 测试降级模式下的锁功能
        assertTrue(inMemoryJobLock.tryLock(jobName), "降级模式下应该能获取内存锁");
        assertTrue(inMemoryJobLock.isLocked(jobName), "内存锁应该被持有");
        
        // 测试降级模式下的状态管理
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        
        assertNotNull(status, "降级模式下状态不应为null");
        assertTrue(status.isRunning(), "降级模式下Job应该处于运行状态");
        assertEquals(jobName, status.getJobName(), "Job名称应该正确");
        
        // 完成Job
        JobExecutionResult result = JobExecutionResult.success(1000L, "降级模式成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
        
        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "Job应该完成");
        assertEquals("SUCCESS", status.getLastStatus(), "状态应该为成功");
        
        // 释放锁
        inMemoryJobLock.releaseLock(jobName);
        assertFalse(inMemoryJobLock.isLocked(jobName), "锁应该被释放");
        
        System.out.println("✅ Redis不可用时的降级处理测试通过");
        System.out.println("   - 内存锁降级: ✅");
        System.out.println("   - 状态管理降级: ✅");
        System.out.println("   - 完整工作流程: ✅");
    }

    @Test
    @Order(2)
    @DisplayName("2. 内存存储功能完整性测试")
    public void testInMemoryStorageCompleteness() {
        String jobName = "memory-storage-job";
        
        // 执行多次Job来测试内存存储
        for (int i = 1; i <= 3; i++) {
            String executionId = "exec-memory-" + String.format("%03d", i);
            
            // 获取锁
            assertTrue(inMemoryJobLock.tryLock(jobName), "应该能获取锁 " + i);
            
            // 记录开始
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            
            // 验证运行状态
            JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
            assertTrue(status.isRunning(), "Job应该处于运行状态 " + i);
            
            // 完成Job
            JobExecutionResult result;
            if (i == 2) {
                result = JobExecutionResult.failure(1500L, "测试失败", "错误详情", 1);
            } else {
                result = JobExecutionResult.success(1000L + i * 100, "测试成功 " + i);
            }
            
            inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
            
            // 验证完成状态
            status = inMemoryJobStatusService.getJobStatus(jobName);
            assertFalse(status.isRunning(), "Job应该完成 " + i);
            
            // 释放锁
            inMemoryJobLock.releaseLock(jobName);
            assertFalse(inMemoryJobLock.isLocked(jobName), "锁应该被释放 " + i);
        }
        
        // 验证执行历史
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertNotNull(history, "执行历史不应为null");
        assertEquals(3, history.size(), "应该有3条执行历史");
        
        // 验证历史记录顺序（最新的在前）
        assertEquals("exec-memory-003", history.get(0).getExecutionId(), "最新记录应该在前");
        
        // 验证成功和失败记录
        long successCount = history.stream().filter(h -> "SUCCESS".equals(h.getStatus())).count();
        long failureCount = history.stream().filter(h -> "FAIL".equals(h.getStatus())).count();
        
        assertEquals(2, successCount, "应该有2条成功记录");
        assertEquals(1, failureCount, "应该有1条失败记录");
        
        System.out.println("✅ 内存存储功能完整性测试通过");
        System.out.println("   - 多次执行: ✅");
        System.out.println("   - 执行历史: ✅");
        System.out.println("   - 成功记录: " + successCount);
        System.out.println("   - 失败记录: " + failureCount);
    }

    @Test
    @Order(3)
    @DisplayName("3. 降级模式下的并发控制测试")
    public void testConcurrencyControlInDegradationMode() throws InterruptedException {
        String jobName = "concurrent-degradation-job";
        
        // 模拟Redis不可用
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(false);
        
        // 第一个线程获取锁
        assertTrue(inMemoryJobLock.tryLock(jobName), "第一个线程应该能获取锁");
        
        // 模拟另一个线程尝试获取同一个锁
        Thread concurrentThread = new Thread(() -> {
            boolean acquired = inMemoryJobLock.tryLock(jobName);
            assertFalse(acquired, "并发线程不应该能获取已被持有的锁");
        });
        
        concurrentThread.start();
        concurrentThread.join(1000); // 等待1秒
        
        // 验证锁仍被第一个线程持有
        assertTrue(inMemoryJobLock.isLocked(jobName), "锁应该仍被持有");
        assertTrue(inMemoryJobLock.isHeldByCurrentThread(jobName), "锁应该被当前线程持有");
        
        // 释放锁
        inMemoryJobLock.releaseLock(jobName);
        assertFalse(inMemoryJobLock.isLocked(jobName), "锁应该被释放");
        
        // 现在另一个线程应该能获取锁
        Thread secondThread = new Thread(() -> {
            boolean acquired = inMemoryJobLock.tryLock(jobName);
            assertTrue(acquired, "锁释放后其他线程应该能获取");
            inMemoryJobLock.releaseLock(jobName);
        });
        
        secondThread.start();
        secondThread.join(1000);
        
        System.out.println("✅ 降级模式下的并发控制测试通过");
        System.out.println("   - 锁互斥: ✅");
        System.out.println("   - 线程安全: ✅");
    }

    @Test
    @Order(4)
    @DisplayName("4. Redis恢复后的切换机制测试")
    public void testRedisRecoverySwitching() {
        String jobName = "recovery-test-job";
        String executionId = "exec-recovery-001";
        
        // 阶段1：Redis不可用，使用降级模式
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(false);
        
        assertTrue(inMemoryJobLock.tryLock(jobName), "降级模式下应该能获取内存锁");
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        assertTrue(status.isRunning(), "降级模式下Job应该运行");
        
        // 阶段2：模拟Redis恢复
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(true);
        when(redisAvailabilityService.executeWithFallback(any(), any())).thenAnswer(invocation -> {
            // 模拟Redis操作成功
            return "Redis操作成功";
        });
        
        // 完成当前Job（仍在降级模式下）
        JobExecutionResult result = JobExecutionResult.success(1200L, "恢复测试成功");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
        inMemoryJobLock.releaseLock(jobName);
        
        // 验证Job完成
        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "Job应该完成");
        assertEquals("SUCCESS", status.getLastStatus(), "状态应该为成功");
        
        // 验证执行历史
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertEquals(1, history.size(), "应该有1条执行历史");
        assertEquals("SUCCESS", history.get(0).getStatus(), "历史记录应该为成功");
        
        System.out.println("✅ Redis恢复后的切换机制测试通过");
        System.out.println("   - 降级模式执行: ✅");
        System.out.println("   - Redis恢复检测: ✅");
        System.out.println("   - 状态一致性: ✅");
    }

    @Test
    @Order(5)
    @DisplayName("5. 降级功能异常处理测试")
    public void testDegradationErrorHandling() {
        String jobName = "error-handling-job";
        String executionId = "exec-error-001";
        
        // 模拟Redis不可用
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(false);
        
        try {
            // 正常获取锁
            assertTrue(inMemoryJobLock.tryLock(jobName), "应该能获取锁");
            
            // 记录Job开始
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            
            // 模拟Job执行异常
            throw new RuntimeException("模拟Job执行异常");
            
        } catch (Exception e) {
            // 异常处理：强制释放锁，记录失败状态
            inMemoryJobLock.forceReleaseLock(jobName);
            
            JobExecutionResult errorResult = JobExecutionResult.failure(500L, "执行异常", e.getMessage(), 0);
            inMemoryJobStatusService.recordJobCompletion(jobName, executionId, errorResult);
        }
        
        // 验证异常处理结果
        assertFalse(inMemoryJobLock.isLocked(jobName), "异常后锁应该被释放");
        
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "异常后Job不应该运行");
        assertEquals("FAIL", status.getLastStatus(), "状态应该为失败");
        
        // 验证可以重新获取锁
        assertTrue(inMemoryJobLock.tryLock(jobName), "异常处理后应该能重新获取锁");
        inMemoryJobLock.releaseLock(jobName);
        
        System.out.println("✅ 降级功能异常处理测试通过");
        System.out.println("   - 异常锁释放: ✅");
        System.out.println("   - 失败状态记录: ✅");
        System.out.println("   - 锁重新获取: ✅");
    }

    @Test
    @Order(6)
    @DisplayName("6. 降级模式性能验证测试")
    public void testDegradationModePerformance() {
        String jobName = "performance-test-job";
        int testCount = 10;
        
        // 模拟Redis不可用
        when(redisAvailabilityService.isRedisAvailable()).thenReturn(false);
        
        long startTime = System.currentTimeMillis();
        
        // 执行多次操作测试性能
        for (int i = 1; i <= testCount; i++) {
            String executionId = "exec-perf-" + String.format("%03d", i);
            
            // 锁操作
            assertTrue(inMemoryJobLock.tryLock(jobName), "应该能获取锁 " + i);
            
            // 状态操作
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            JobExecutionResult result = JobExecutionResult.success(100L, "性能测试 " + i);
            inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
            
            // 释放锁
            inMemoryJobLock.releaseLock(jobName);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / testCount;
        
        // 验证执行历史
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, testCount);
        assertEquals(testCount, history.size(), "应该有" + testCount + "条执行历史");
        
        // 性能应该是可接受的（每次操作小于100ms）
        assertTrue(avgTime < 100, "平均执行时间应该小于100ms，实际: " + avgTime + "ms");
        
        System.out.println("✅ 降级模式性能验证测试通过");
        System.out.println("   - 总执行次数: " + testCount);
        System.out.println("   - 总耗时: " + totalTime + "ms");
        System.out.println("   - 平均耗时: " + String.format("%.2f", avgTime) + "ms");
        System.out.println("   - 历史记录数: " + history.size());
    }

    @AfterAll
    static void teardownClass() {
        System.out.println("=== Job降级功能测试结束 ===");
    }
}
