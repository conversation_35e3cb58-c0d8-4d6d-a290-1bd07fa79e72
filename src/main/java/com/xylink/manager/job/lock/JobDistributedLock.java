package com.xylink.manager.job.lock;

import com.xylink.manager.job.constant.JobConstants;
import com.xylink.manager.job.config.RedisDebounceConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Job分布式锁服务
 * 基于Redisson实现分布式锁，确保多活环境下任务的唯一性执行
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class JobDistributedLock {

    @Autowired(required = false)
    private RedissonClient redissonClient;

    @Autowired
    private InMemoryJobLock inMemoryJobLock;

    @Autowired
    private RedisDebounceConfig redisDebounceConfig;

    /**
     * 用于异步Redis检查的线程池
     */
    private static final ScheduledExecutorService redisCheckExecutor =
        Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "redis-check-thread");
            t.setDaemon(true);
            return t;
        });

    /**
     * Redis是否可用的标志
     */
    private final AtomicBoolean redisAvailable = new AtomicBoolean(true);

    /**
     * 上次检查Redis可用性的时间
     */
    private volatile long lastRedisCheckTime = 0;

    /**
     * Redis连续可用次数（防抖动）
     */
    private final AtomicInteger redisConsecutiveSuccessCount = new AtomicInteger(0);

    /**
     * Redis连续失败次数（防抖动）
     */
    private final AtomicInteger redisConsecutiveFailureCount = new AtomicInteger(0);

    /**
     * 检查Redis是否恢复可用（带防抖动机制）
     * 使用异步方式避免阻塞主线程
     */
    private void checkRedisRecovery() {
        if (redissonClient == null) {
            return;
        }

        long currentTime = System.currentTimeMillis();

        // 如果Redis已经可用，定期检查是否仍然稳定
        if (redisAvailable.get()) {
            // 每隔检查间隔验证Redis稳定性
            if (currentTime - lastRedisCheckTime < redisDebounceConfig.getRedisCheckInterval()) {
                return;
            }

            lastRedisCheckTime = currentTime;

            // 异步检查Redis稳定性，避免阻塞主线程
            redisCheckExecutor.submit(() -> {
                try {
                    redissonClient.getBucket(RedisDebounceConfig.HEALTH_CHECK_KEY).isExists();
                    // Redis仍然可用，重置失败计数
                    redisConsecutiveFailureCount.set(0);
                    log.debug("Redis连接稳定性检查通过");
                } catch (Exception e) {
                    // Redis出现问题，增加失败计数
                    int failureCount = redisConsecutiveFailureCount.incrementAndGet();
                    log.warn("Redis连接检查失败 ({}/{}): {}", failureCount, redisDebounceConfig.getRequiredFailureCount(), e.getMessage());

                    // 连续失败达到阈值，切换到内存模式
                    if (failureCount >= redisDebounceConfig.getRequiredFailureCount()) {
                        redisAvailable.set(false);
                        redisConsecutiveSuccessCount.set(0);
                        log.warn("Redis连续失败{}次，切换到内存锁模式", failureCount);
                    }
                }
            });
            return;
        }

        // Redis不可用，检查是否恢复
        if (currentTime - lastRedisCheckTime < redisDebounceConfig.getRedisCheckInterval()) {
            return;
        }

        lastRedisCheckTime = currentTime;

        // 异步检查Redis恢复，避免阻塞主线程
        redisCheckExecutor.submit(() -> {
            try {
                redissonClient.getBucket(RedisDebounceConfig.HEALTH_CHECK_KEY).isExists();
                // Redis操作成功，增加成功计数
                int successCount = redisConsecutiveSuccessCount.incrementAndGet();
                redisConsecutiveFailureCount.set(0);

                log.info("Redis连接检查成功 ({}/{})", successCount, redisDebounceConfig.getRequiredSuccessCount());

                // 连续成功达到阈值，切换回分布式模式
                if (successCount >= redisDebounceConfig.getRequiredSuccessCount()) {
                    redisAvailable.set(true);
                    log.info("Redis连续成功{}次，切换回分布式锁模式", successCount);
                }
            } catch (Exception e) {
                // 重置成功计数
                redisConsecutiveSuccessCount.set(0);
                log.debug("Redis仍不可用，继续使用内存锁模式: {}", e.getMessage());
            }
        });
    }

    /**
     * 尝试获取分布式锁
     *
     * @param jobName 任务名称
     * @return 获取成功返回true，失败返回false
     */
    public boolean tryLock(String jobName) {
        // 检查Redis是否恢复
        checkRedisRecovery();

        // 如果Redis不可用或未配置，使用内存锁
        if (redissonClient == null || !redisAvailable.get()) {
            return tryInMemoryLock(jobName);
        }

        String lockKey = JobConstants.LOCK_KEY_PREFIX + jobName;

        try {
            RLock lock = redissonClient.getLock(lockKey);

            // 尝试获取锁，等待时间0秒，锁自动释放时间30秒
            // Redisson会自动启动看门狗机制续期
            boolean acquired = lock.tryLock(0, 30, TimeUnit.SECONDS);

            if (acquired) {
                log.info("成功获取Job分布式锁: {}", lockKey);
            } else {
                log.info("获取Job分布式锁失败，任务可能正在其他节点执行: {}", lockKey);
            }

            return acquired;
        } catch (InterruptedException e) {
            log.warn("获取Job分布式锁被中断: {}", lockKey, e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.error("获取Job分布式锁异常，降级到内存锁模式: {}", lockKey, e);
            // Redis异常时，标记Redis不可用并降级到内存锁
            redisAvailable.set(false);
            return tryInMemoryLock(jobName);
        }
    }

    /**
     * 尝试获取内存锁（降级模式）
     */
    private boolean tryInMemoryLock(String jobName) {
        log.info("使用内存锁模式: jobName={}", jobName);
        return inMemoryJobLock.tryLock(jobName);
    }

    /**
     * 释放分布式锁
     *
     * @param jobName 任务名称
     */
    public void releaseLock(String jobName) {
        // 检查Redis是否恢复
        checkRedisRecovery();

        // 如果Redis不可用或未配置，使用内存锁
        if (redissonClient == null || !redisAvailable.get()) {
            releaseInMemoryLock(jobName);
            return;
        }

        String lockKey = JobConstants.LOCK_KEY_PREFIX + jobName;

        try {
            RLock lock = redissonClient.getLock(lockKey);

            // 只有当前线程持有锁时才能释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("成功释放Job分布式锁: {}", lockKey);
            } else {
                log.warn("当前线程未持有锁，无法释放: {}", lockKey);
            }
        } catch (Exception e) {
            log.error("释放Job分布式锁异常，尝试释放内存锁: {}", lockKey, e);
            // Redis异常时，尝试释放内存锁
            releaseInMemoryLock(jobName);
        }
    }

    /**
     * 释放内存锁（降级模式）
     */
    private void releaseInMemoryLock(String jobName) {
        log.debug("释放内存锁: jobName={}", jobName);
        inMemoryJobLock.releaseLock(jobName);
    }

    /**
     * 强制释放分布式锁（紧急情况使用）
     *
     * @param jobName 任务名称
     */
    public void forceReleaseLock(String jobName) {
        // 如果Redis不可用或未配置，使用内存锁
        if (redissonClient == null || !redisAvailable.get()) {
            inMemoryJobLock.forceReleaseLock(jobName);
            return;
        }

        String lockKey = JobConstants.LOCK_KEY_PREFIX + jobName;

        try {
            RLock lock = redissonClient.getLock(lockKey);

            if (lock.isLocked()) {
                lock.forceUnlock();
                log.warn("强制释放Job分布式锁: {}", lockKey);
            } else {
                log.info("锁未被持有，无需强制释放: {}", lockKey);
            }
        } catch (Exception e) {
            log.error("强制释放Job分布式锁异常，尝试强制释放内存锁: {}", lockKey, e);
            inMemoryJobLock.forceReleaseLock(jobName);
        }
    }

    /**
     * 检查锁是否被持有
     *
     * @param jobName 任务名称
     * @return 锁是否被持有
     */
    public boolean isLocked(String jobName) {
        // 检查Redis是否恢复
        checkRedisRecovery();

        // 如果Redis不可用或未配置，使用内存锁
        if (redissonClient == null || !redisAvailable.get()) {
            return inMemoryJobLock.isLocked(jobName);
        }

        try {
            String lockKey = JobConstants.LOCK_KEY_PREFIX + jobName;
            RLock lock = redissonClient.getLock(lockKey);
            return lock.isLocked();
        } catch (Exception e) {
            log.error("检查分布式锁状态异常，使用内存锁状态: jobName={}", jobName, e);
            return inMemoryJobLock.isLocked(jobName);
        }
    }

    /**
     * 检查锁是否被当前线程持有
     *
     * @param jobName 任务名称
     * @return 锁是否被当前线程持有
     */
    public boolean isHeldByCurrentThread(String jobName) {
        // 如果Redis不可用或未配置，使用内存锁
        if (redissonClient == null || !redisAvailable.get()) {
            return inMemoryJobLock.isHeldByCurrentThread(jobName);
        }

        try {
            String lockKey = JobConstants.LOCK_KEY_PREFIX + jobName;
            RLock lock = redissonClient.getLock(lockKey);
            return lock.isHeldByCurrentThread();
        } catch (Exception e) {
            log.error("检查分布式锁持有状态异常，使用内存锁状态: jobName={}", jobName, e);
            return inMemoryJobLock.isHeldByCurrentThread(jobName);
        }
    }

    /**
     * 手动检查Redis恢复状态（带防抖动机制）
     *
     * @return Redis是否可用
     */
    public boolean checkAndRecoverRedis() {
        if (redissonClient == null) {
            return false;
        }

        try {
            // 强制检查Redis连接
            redissonClient.getBucket(RedisDebounceConfig.HEALTH_CHECK_KEY).isExists();

            boolean wasUnavailable = !redisAvailable.get();

            if (wasUnavailable) {
                // Redis从不可用恢复，需要连续成功验证
                int successCount = redisConsecutiveSuccessCount.incrementAndGet();
                redisConsecutiveFailureCount.set(0);

                log.info("手动检查Redis连接成功 ({}/{})", successCount, redisDebounceConfig.getRequiredSuccessCount());

                if (successCount >= redisDebounceConfig.getRequiredSuccessCount()) {
                    redisAvailable.set(true);
                    lastRedisCheckTime = System.currentTimeMillis();
                    log.info("手动检查Redis连续成功{}次，已切换回分布式锁模式", successCount);
                    return true;
                } else {
                    log.info("Redis连接成功，但需要连续成功{}次才能切换回分布式模式", redisDebounceConfig.getRequiredSuccessCount());
                    return false;
                }
            } else {
                // Redis已经可用，重置失败计数
                redisConsecutiveFailureCount.set(0);
                lastRedisCheckTime = System.currentTimeMillis();
                log.debug("手动检查Redis连接稳定");
                return true;
            }

        } catch (Exception e) {
            log.debug("手动检查Redis仍不可用: {}", e.getMessage());

            // 重置成功计数，增加失败计数
            redisConsecutiveSuccessCount.set(0);

            if (redisAvailable.get()) {
                int failureCount = redisConsecutiveFailureCount.incrementAndGet();
                log.warn("手动检查Redis失败 ({}/{}): {}", failureCount, redisDebounceConfig.getRequiredFailureCount(), e.getMessage());

                if (failureCount >= redisDebounceConfig.getRequiredFailureCount()) {
                    redisAvailable.set(false);
                    log.warn("Redis连续失败{}次，切换到内存锁模式", failureCount);
                }
            }

            return false;
        }
    }

    /**
     * 获取当前锁模式
     *
     * @return 锁模式描述
     */
    public String getLockMode() {
        if (redissonClient == null) {
            return "MEMORY_MODE (Redis未配置)";
        } else if (!redisAvailable.get()) {
            return "MEMORY_MODE (Redis不可用)";
        } else {
            return "DISTRIBUTED_MODE (Redis可用)";
        }
    }
}