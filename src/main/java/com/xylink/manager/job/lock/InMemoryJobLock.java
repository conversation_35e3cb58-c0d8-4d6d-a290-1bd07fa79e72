package com.xylink.manager.job.lock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;

/**
 * 内存模式的Job锁实现
 * 用于Redis不可用时的单机模式降级
 */
@Slf4j
@Component
public class InMemoryJobLock {

    /**
     * 存储各个Job的锁对象
     */
    private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

    /**
     * 存储锁的持有线程信息
     */
    private final ConcurrentHashMap<String, Thread> lockHolders = new ConcurrentHashMap<>();

    /**
     * 尝试获取锁
     * 
     * @param jobName 任务名称
     * @return 获取成功返回true，失败返回false
     */
    public boolean tryLock(String jobName) {
        try {
            ReentrantLock lock = lockMap.computeIfAbsent(jobName, k -> new ReentrantLock());
            
            // 尝试获取锁，不等待
            boolean acquired = lock.tryLock(0, TimeUnit.SECONDS);
            
            if (acquired) {
                lockHolders.put(jobName, Thread.currentThread());
                log.debug("成功获取内存锁: jobName={}, thread={}", jobName, Thread.currentThread().getName());
            } else {
                log.debug("获取内存锁失败，任务可能正在执行: jobName={}", jobName);
            }
            
            return acquired;
        } catch (InterruptedException e) {
            log.warn("获取内存锁被中断: jobName={}", jobName, e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.error("获取内存锁异常: jobName={}", jobName, e);
            return false;
        }
    }

    /**
     * 释放锁
     * 
     * @param jobName 任务名称
     */
    public void releaseLock(String jobName) {
        try {
            ReentrantLock lock = lockMap.get(jobName);
            if (lock != null && lock.isHeldByCurrentThread()) {
                lockHolders.remove(jobName);
                lock.unlock();
                log.debug("成功释放内存锁: jobName={}, thread={}", jobName, Thread.currentThread().getName());
            } else {
                log.warn("当前线程未持有锁，无法释放: jobName={}, thread={}", 
                        jobName, Thread.currentThread().getName());
            }
        } catch (Exception e) {
            log.error("释放内存锁异常: jobName={}", jobName, e);
        }
    }

    /**
     * 强制释放锁（紧急情况使用）
     * 
     * @param jobName 任务名称
     */
    public void forceReleaseLock(String jobName) {
        try {
            ReentrantLock lock = lockMap.get(jobName);
            if (lock != null && lock.isLocked()) {
                // 记录原持有线程
                Thread originalHolder = lockHolders.get(jobName);
                
                // 强制释放锁（注意：这可能导致数据不一致）
                while (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                
                lockHolders.remove(jobName);
                log.warn("强制释放内存锁: jobName={}, originalHolder={}, currentThread={}", 
                        jobName, originalHolder != null ? originalHolder.getName() : "unknown", 
                        Thread.currentThread().getName());
            } else {
                log.info("锁未被持有，无需强制释放: jobName={}", jobName);
            }
        } catch (Exception e) {
            log.error("强制释放内存锁异常: jobName={}", jobName, e);
        }
    }

    /**
     * 检查锁是否被持有
     * 
     * @param jobName 任务名称
     * @return 锁是否被持有
     */
    public boolean isLocked(String jobName) {
        ReentrantLock lock = lockMap.get(jobName);
        return lock != null && lock.isLocked();
    }

    /**
     * 检查锁是否被当前线程持有
     * 
     * @param jobName 任务名称
     * @return 锁是否被当前线程持有
     */
    public boolean isHeldByCurrentThread(String jobName) {
        ReentrantLock lock = lockMap.get(jobName);
        return lock != null && lock.isHeldByCurrentThread();
    }

    /**
     * 获取锁的持有线程
     * 
     * @param jobName 任务名称
     * @return 持有锁的线程，如果没有则返回null
     */
    public Thread getLockHolder(String jobName) {
        return lockHolders.get(jobName);
    }

    /**
     * 获取当前所有被锁定的Job名称
     * 
     * @return 被锁定的Job名称集合
     */
    public java.util.Set<String> getLockedJobs() {
        return lockMap.entrySet().stream()
                .filter(entry -> entry.getValue().isLocked())
                .map(java.util.Map.Entry::getKey)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 清理所有锁（用于测试或紧急情况）
     */
    public void clearAllLocks() {
        log.warn("清理所有内存锁，当前锁定的Job: {}", getLockedJobs());
        lockHolders.clear();
        lockMap.clear();
    }
}
