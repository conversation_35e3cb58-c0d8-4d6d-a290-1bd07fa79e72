package com.xylink.manager.job.scheduler;

import com.xylink.manager.job.config.JobConfigLoader;
import com.xylink.manager.job.exception.JobConfigException;
import com.xylink.manager.job.executor.JobExecutor;
import com.xylink.manager.job.metrics.JobMetrics;
import com.xylink.manager.job.model.JobConfig;
import com.xylink.manager.job.model.JobType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Job调度管理器
 * 负责管理所有Job的调度，确保每个Job的调度互不干扰
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class JobScheduler {

    @Autowired
    private JobConfigLoader jobConfigLoader;

    @Autowired
    private JobExecutor jobExecutor;

    @Autowired
    private JobMetrics jobMetrics;

    /**
     * 独立的任务调度器
     */
    private TaskScheduler taskScheduler;

    /**
     * 已调度的任务映射表，key为jobName，value为ScheduledFuture
     * 使用ConcurrentHashMap确保线程安全
     */
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    /**
     * 当前生效的Job配置映射表，key为jobName，value为JobConfig
     * 用于支持动态配置更新和Job控制
     */
    private final Map<String, JobConfig> currentJobConfigs = new ConcurrentHashMap<>();

    /**
     * 调度器初始化状态标志
     */
    private final AtomicBoolean initialized = new AtomicBoolean(false);

    /**
     * 调度器关闭状态标志
     */
    private final AtomicBoolean shutdown = new AtomicBoolean(false);

    @PostConstruct
    public void init() {
        try {
            log.info("开始初始化JobScheduler...");

            // 初始化独立的任务调度器
            initTaskScheduler();

            // 加载并调度所有Job
            loadAndScheduleJobs();

            initialized.set(true);
            log.info("JobScheduler初始化完成，已调度{}个Job", scheduledTasks.size());

        } catch (Exception e) {
            log.error("JobScheduler初始化失败", e);
            throw new RuntimeException("JobScheduler初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (shutdown.compareAndSet(false, true)) {
            log.info("开始关闭JobScheduler...");

            // 取消所有已调度的任务
            unscheduleAllJobs();

            // 关闭任务调度器
            if (taskScheduler instanceof ThreadPoolTaskScheduler) {
                ((ThreadPoolTaskScheduler) taskScheduler).shutdown();
                log.info("任务调度器已关闭");
            }

            log.info("JobScheduler已关闭");
        }
    }

    /**
     * 初始化任务调度器
     */
    private void initTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10); // 调度线程池大小
        scheduler.setThreadNamePrefix("job-scheduler-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(30);
        scheduler.setRejectedExecutionHandler((r, executor) -> {
            log.error("Job调度线程池队列已满，拒绝调度任务");
        });
        scheduler.initialize();

        this.taskScheduler = scheduler;
        log.info("任务调度器初始化完成，线程池大小: {}", scheduler.getPoolSize());
    }

    /**
     * 加载并调度所有Job
     */
    private void loadAndScheduleJobs() throws JobConfigException {
        List<JobConfig> jobConfigs = jobConfigLoader.loadJobConfigs();
        
        for (JobConfig jobConfig : jobConfigs) {
            if (jobConfig.isEnabled()) {
                scheduleJob(jobConfig);
            } else {
                log.info("Job已禁用，跳过调度: jobName={}", jobConfig.getJobName());
                // 仍然保存配置，以便后续启用
                currentJobConfigs.put(jobConfig.getJobName(), jobConfig);
            }
        }
    }

    /**
     * 调度单个Job
     * 
     * @param jobConfig Job配置
     */
    public synchronized void scheduleJob(JobConfig jobConfig) {
        if (shutdown.get()) {
            log.warn("调度器已关闭，无法调度Job: jobName={}", jobConfig.getJobName());
            return;
        }

        String jobName = jobConfig.getJobName();
        
        try {
            // 如果Job已经被调度，先取消调度
            unscheduleJob(jobName);

            // 创建Cron触发器
            CronTrigger cronTrigger = new CronTrigger(jobConfig.getCron());

            // 调度任务
            ScheduledFuture<?> scheduledFuture = taskScheduler.schedule(
                    () -> executeJobSafely(jobConfig),
                    cronTrigger
            );

            // 保存调度信息
            scheduledTasks.put(jobName, scheduledFuture);
            currentJobConfigs.put(jobName, jobConfig);

            log.info("Job调度成功: jobName={}, cron={}, enabled={}", 
                    jobName, jobConfig.getCron(), jobConfig.isEnabled());

        } catch (Exception e) {
            log.error("Job调度失败: jobName={}, cron={}", jobName, jobConfig.getCron(), e);
            throw new RuntimeException("Job调度失败: " + jobName, e);
        }
    }

    /**
     * 取消调度单个Job
     * 
     * @param jobName Job名称
     */
    public synchronized void unscheduleJob(String jobName) {
        ScheduledFuture<?> scheduledFuture = scheduledTasks.remove(jobName);
        if (scheduledFuture != null) {
            try {
                scheduledFuture.cancel(false); // 不中断正在执行的任务
                log.info("Job调度已取消: jobName={}", jobName);
            } catch (Exception e) {
                log.error("取消Job调度异常: jobName={}", jobName, e);
            }
        }
    }

    /**
     * 取消所有Job的调度
     */
    private void unscheduleAllJobs() {
        log.info("开始取消所有Job调度，当前调度数量: {}", scheduledTasks.size());
        
        for (Map.Entry<String, ScheduledFuture<?>> entry : scheduledTasks.entrySet()) {
            String jobName = entry.getKey();
            ScheduledFuture<?> scheduledFuture = entry.getValue();
            
            try {
                scheduledFuture.cancel(false); // 不中断正在执行的任务
                log.debug("Job调度已取消: jobName={}", jobName);
            } catch (Exception e) {
                log.error("取消Job调度异常: jobName={}", jobName, e);
            }
        }
        
        scheduledTasks.clear();
        currentJobConfigs.clear();
        log.info("所有Job调度已取消");
    }

    /**
     * 重新加载所有Job配置并重新调度
     * 实现原子性的配置重载，确保重载过程中Job执行不受影响
     * 
     * @throws JobConfigException 配置加载或验证失败时抛出
     */
    public synchronized void reloadJobs() throws JobConfigException {
        if (shutdown.get()) {
            throw new JobConfigException("调度器已关闭，无法重新加载配置");
        }

        log.info("开始重新加载Job配置...");

        try {
            // 1. 加载新的配置（先验证，确保配置有效）
            List<JobConfig> newJobConfigs = jobConfigLoader.loadJobConfigs();
            log.info("新配置加载成功，包含{}个Job", newJobConfigs.size());

            // 2. 备份当前调度状态
            Map<String, ScheduledFuture<?>> backupScheduledTasks = new ConcurrentHashMap<>(scheduledTasks);
            Map<String, JobConfig> backupJobConfigs = new ConcurrentHashMap<>(currentJobConfigs);

            try {
                // 3. 取消所有当前调度
                unscheduleAllJobs();

                // 4. 应用新配置
                for (JobConfig jobConfig : newJobConfigs) {
                    if (jobConfig.isEnabled()) {
                        scheduleJob(jobConfig);
                    } else {
                        // 保存禁用的Job配置
                        currentJobConfigs.put(jobConfig.getJobName(), jobConfig);
                    }
                }

                log.info("Job配置重新加载成功，当前调度数量: {}", scheduledTasks.size());

            } catch (Exception e) {
                // 5. 如果新配置应用失败，恢复原有调度状态
                log.error("应用新配置失败，正在恢复原有调度状态", e);
                
                // 清理失败的调度
                unscheduleAllJobs();
                
                // 恢复原有调度
                scheduledTasks.putAll(backupScheduledTasks);
                currentJobConfigs.putAll(backupJobConfigs);
                
                throw new JobConfigException("配置重载失败，已恢复原有调度状态: " + e.getMessage(), e);
            }

        } catch (JobConfigException e) {
            log.error("重新加载Job配置失败", e);
            throw e;
        } catch (Exception e) {
            log.error("重新加载Job配置时发生未预期异常", e);
            throw new JobConfigException("重新加载Job配置时发生未预期异常: " + e.getMessage(), e);
        }
    }

    /**
     * 启用指定Job
     * 
     * @param jobName Job名称
     * @throws JobConfigException 如果Job不存在或启用失败
     */
    public synchronized void enableJob(String jobName) throws JobConfigException {
        JobConfig jobConfig = currentJobConfigs.get(jobName);
        if (jobConfig == null) {
            throw new JobConfigException("Job不存在: " + jobName);
        }

        if (jobConfig.isEnabled()) {
            log.info("Job已经是启用状态: jobName={}", jobName);
            return;
        }

        try {
            // 更新配置状态
            jobConfig.setEnabled(true);
            
            // 调度Job
            scheduleJob(jobConfig);
            
            log.info("Job已启用: jobName={}", jobName);
            
        } catch (Exception e) {
            // 恢复原状态
            jobConfig.setEnabled(false);
            log.error("启用Job失败: jobName={}", jobName, e);
            throw new JobConfigException("启用Job失败: " + jobName + ", " + e.getMessage(), e);
        }
    }

    /**
     * 禁用指定Job
     * 
     * @param jobName Job名称
     * @throws JobConfigException 如果Job不存在
     */
    public synchronized void disableJob(String jobName) throws JobConfigException {
        JobConfig jobConfig = currentJobConfigs.get(jobName);
        if (jobConfig == null) {
            throw new JobConfigException("Job不存在: " + jobName);
        }

        if (!jobConfig.isEnabled()) {
            log.info("Job已经是禁用状态: jobName={}", jobName);
            return;
        }

        try {
            // 取消调度
            unscheduleJob(jobName);
            
            // 更新配置状态
            jobConfig.setEnabled(false);
            
            log.info("Job已禁用: jobName={}", jobName);
            
        } catch (Exception e) {
            log.error("禁用Job失败: jobName={}", jobName, e);
            throw new JobConfigException("禁用Job失败: " + jobName + ", " + e.getMessage(), e);
        }
    }

    /**
     * 手动触发指定Job
     * 
     * @param jobName Job名称
     * @throws JobConfigException 如果Job不存在
     */
    public void triggerJob(String jobName) throws JobConfigException {
        JobConfig jobConfig = currentJobConfigs.get(jobName);
        if (jobConfig == null) {
            throw new JobConfigException("Job不存在: " + jobName);
        }

        log.info("手动触发Job: jobName={}", jobName);
        
        // 异步执行，不阻塞调用线程
        try {
            jobExecutor.executeJobAsync(jobConfig);
            log.info("Job手动触发成功: jobName={}", jobName);
        } catch (Exception e) {
            log.error("Job手动触发失败: jobName={}", jobName, e);
            throw new JobConfigException("Job手动触发失败: " + jobName + ", " + e.getMessage(), e);
        }
    }

    /**
     * 安全执行Job，确保一个Job的调度异常不会影响其他Job的调度
     */
    private void executeJobSafely(JobConfig jobConfig) {
        String jobName = jobConfig.getJobName();
        
        try {
            // 检查Job是否仍然启用（防止在调度间隙被禁用）
            JobConfig currentConfig = currentJobConfigs.get(jobName);
            if (currentConfig == null || !currentConfig.isEnabled()) {
                log.debug("Job已被禁用或删除，跳过执行: jobName={}", jobName);
                return;
            }

            // 异步执行Job，不阻塞调度线程
            jobExecutor.executeJobAsync(jobConfig);
            
        } catch (Exception e) {
            // 捕获所有异常，确保不影响其他Job的调度
            log.error("Job调度执行异常，但不影响其他Job调度: jobName={}", jobName, e);
        }
    }

    /**
     * 获取当前所有Job配置
     * 
     * @return Job配置列表
     */
    public List<JobConfig> getAllJobConfigs() {
        return new ArrayList<>(currentJobConfigs.values());
    }

    /**
     * 获取指定Job配置
     * 
     * @param jobName Job名称
     * @return Job配置，如果不存在返回null
     */
    public JobConfig getJobConfig(String jobName) {
        return currentJobConfigs.get(jobName);
    }

    /**
     * 获取当前调度的Job数量
     * 
     * @return 调度的Job数量
     */
    public int getScheduledJobCount() {
        return scheduledTasks.size();
    }

    /**
     * 获取调度器状态信息
     * 
     * @return 调度器状态描述
     */
    public String getSchedulerStatus() {
        if (!initialized.get()) {
            return "调度器未初始化";
        }
        
        if (shutdown.get()) {
            return "调度器已关闭";
        }
        
        int totalJobs = currentJobConfigs.size();
        int scheduledJobs = scheduledTasks.size();
        int enabledJobs = (int) currentJobConfigs.values().stream()
                .filter(JobConfig::isEnabled)
                .count();
        
        return String.format("调度器运行中: 总Job数=%d, 已调度=%d, 已启用=%d", 
                totalJobs, scheduledJobs, enabledJobs);
    }

    /**
     * 检查调度器是否已初始化
     * 
     * @return true如果已初始化
     */
    public boolean isInitialized() {
        return initialized.get();
    }

    /**
     * 检查调度器是否已关闭
     * 
     * @return true如果已关闭
     */
    public boolean isShutdown() {
        return shutdown.get();
    }
}