# JobScheduler 任务调度器

## 概述

JobScheduler是配置驱动的Job调度平台的核心组件，负责管理所有Job的调度，确保每个Job的调度互不干扰。

## 主要功能

### 1. 独立调度管理
- 使用Spring TaskScheduler实现基于Cron的任务调度
- 为每个Job创建独立的ScheduledFuture，确保调度互不干扰
- 管理scheduledTasks映射表，确保Job配置变更时的原子性操作

### 2. 动态配置重载
- 支持运行时重新加载Job配置
- 原子性的配置重载逻辑，确保重载过程中Job执行不受影响
- 配置验证失败时自动回滚，不影响现有任务的执行
- 按Job粒度的配置更新，避免全量重载影响所有Job

### 3. Job控制操作
- 支持手动触发Job执行
- 支持动态启用/禁用Job
- 支持强制释放分布式锁（紧急情况）

### 4. 调度隔离保障
- 每个Job的调度异常不会影响其他Job的调度
- 独立的线程池确保调度线程与执行线程分离
- 完善的异常处理机制

## 核心方法

### 调度管理
```java
// 调度单个Job
public void scheduleJob(JobConfig jobConfig)

// 取消调度单个Job
public void unscheduleJob(String jobName)

// 重新加载所有Job配置
public void reloadJobs() throws JobConfigException
```

### Job控制
```java
// 启用Job
public void enableJob(String jobName) throws JobConfigException

// 禁用Job
public void disableJob(String jobName) throws JobConfigException

// 手动触发Job
public void triggerJob(String jobName) throws JobConfigException
```

### 状态查询
```java
// 获取所有Job配置
public List<JobConfig> getAllJobConfigs()

// 获取指定Job配置
public JobConfig getJobConfig(String jobName)

// 获取调度器状态
public String getSchedulerStatus()
```

## 使用示例

### 基本使用
```java
@Autowired
private JobScheduler jobScheduler;

// 手动触发Job
jobScheduler.triggerJob("my-job");

// 禁用Job
jobScheduler.disableJob("my-job");

// 重新加载配置
jobScheduler.reloadJobs();
```

### 状态监控
```java
// 获取调度器状态
String status = jobScheduler.getSchedulerStatus();
System.out.println(status);
// 输出: 调度器运行中: 总Job数=5, 已调度=3, 已启用=4

// 获取所有Job配置
List<JobConfig> configs = jobScheduler.getAllJobConfigs();
for (JobConfig config : configs) {
    System.out.println("Job: " + config.getJobName() + 
                      ", 启用: " + config.isEnabled() + 
                      ", Cron: " + config.getCron());
}
```

## 线程安全

JobScheduler的所有公共方法都是线程安全的：
- 使用ConcurrentHashMap存储调度状态
- 关键操作使用synchronized确保原子性
- 配置重载过程中的状态备份和恢复机制

## 异常处理

### 配置重载异常
- 配置文件解析失败：抛出JobConfigException，保持原有配置
- 配置验证失败：抛出JobConfigException，保持原有配置
- 调度失败：自动回滚到原有配置

### 调度执行异常
- Job执行异常不会影响调度器运行
- 异常信息会被记录到日志中
- 其他Job的调度不受影响

## 生命周期管理

### 初始化
- @PostConstruct自动初始化TaskScheduler
- 自动加载并调度所有启用的Job
- 设置初始化状态标志

### 关闭
- @PreDestroy自动关闭所有调度
- 优雅关闭TaskScheduler，等待正在执行的任务完成
- 清理所有内部状态

## 监控指标

JobScheduler提供以下监控信息：
- 总Job数量
- 已调度Job数量
- 已启用Job数量
- 调度器运行状态
- 线程池状态（通过JobExecutor）

## 最佳实践

1. **配置管理**
   - 定期备份Job配置文件
   - 在生产环境中谨慎使用配置重载功能
   - 监控配置重载的成功率

2. **性能优化**
   - 合理设置Job的执行频率，避免过于频繁的调度
   - 监控线程池的使用情况
   - 定期清理不再使用的Job配置

3. **故障处理**
   - 监控Job的执行状态和失败率
   - 设置合理的告警阈值
   - 准备紧急情况下的手动干预方案

## 相关组件

- **JobConfigLoader**: 负责加载和验证Job配置
- **JobExecutor**: 负责实际执行Job任务
- **JobStatusService**: 负责管理Job状态信息
- **JobDistributedLock**: 负责分布式锁管理