package com.xylink.manager.job.util;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.UUID;

/**
 * Job工具类
 */
public class JobUtils {
    
    private static final String NODE_ID;
    
    static {
        String nodeId;
        try {
            nodeId = InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            nodeId = "unknown-node";
        }
        NODE_ID = nodeId;
    }
    
    /**
     * 生成唯一的执行ID
     * 
     * @return 执行ID
     */
    public static String generateExecutionId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 获取当前节点ID
     * 
     * @return 节点ID
     */
    public static String getNodeId() {
        return NODE_ID;
    }
    
    /**
     * 生成分布式锁的key
     * 
     * @param jobName Job名称
     * @return 锁key
     */
    public static String getLockKey(String jobName) {
        return "job_lock:" + jobName;
    }
    
    /**
     * 生成Job状态的key
     * 
     * @param jobName Job名称
     * @return 状态key
     */
    public static String getStatusKey(String jobName) {
        return "job_status:" + jobName;
    }
    
    /**
     * 生成正在运行Job的key
     * 
     * @param jobName Job名称
     * @return 运行状态key
     */
    public static String getRunningKey(String jobName) {
        return "job_running:" + jobName;
    }
    
    /**
     * 生成Job执行历史的key
     * 
     * @param jobName Job名称
     * @return 历史key
     */
    public static String getHistoryKey(String jobName) {
        return "job_history:" + jobName;
    }
}