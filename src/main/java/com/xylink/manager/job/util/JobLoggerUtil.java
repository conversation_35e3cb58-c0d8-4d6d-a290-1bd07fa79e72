package com.xylink.manager.job.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xylink.manager.job.constant.JobConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Job日志工具类
 * 提供日志脱敏和格式化功能
 */
@Slf4j
public class JobLoggerUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String MASK_VALUE = "***";
    
    /**
     * 敏感字段名称模式（不区分大小写）
     */
    private static final Pattern SENSITIVE_FIELD_PATTERN;
    
    /**
     * 敏感数据值模式
     */
    private static final Map<String, Pattern> SENSITIVE_VALUE_PATTERNS = new HashMap<>();
    
    /**
     * URL参数脱敏模式
     */
    private static final Pattern URL_PARAM_PATTERN = Pattern.compile("([?&](?:password|token|key|secret|auth)=)[^&]*", Pattern.CASE_INSENSITIVE);
    
    /**
     * 邮箱脱敏模式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})");
    
    /**
     * 手机号脱敏模式
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile("(1[3-9]\\d)(\\d{4})(\\d{4})");
    
    /**
     * 身份证号脱敏模式
     */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("(\\d{6})(\\d{8})(\\d{4})");
    
    /**
     * 银行卡号脱敏模式
     */
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile("(\\d{4})(\\d{8,12})(\\d{4})");
    
    static {
        String[] sensitiveFields = JobConstants.SensitiveFields.FIELD_NAMES;
        String pattern = "(?i).*(" + String.join("|", sensitiveFields) + ").*";
        SENSITIVE_FIELD_PATTERN = Pattern.compile(pattern);
        
        // 初始化敏感数据值模式
        SENSITIVE_VALUE_PATTERNS.put("password", Pattern.compile("(?i)(password[\"'\\s]*[:=][\"'\\s]*)([^\"'\\s,}]+)", Pattern.CASE_INSENSITIVE));
        SENSITIVE_VALUE_PATTERNS.put("token", Pattern.compile("(?i)(token[\"'\\s]*[:=][\"'\\s]*)([^\"'\\s,}]+)", Pattern.CASE_INSENSITIVE));
        SENSITIVE_VALUE_PATTERNS.put("apikey", Pattern.compile("(?i)(api[_-]?key[\"'\\s]*[:=][\"'\\s]*)([^\"'\\s,}]+)", Pattern.CASE_INSENSITIVE));
        SENSITIVE_VALUE_PATTERNS.put("secret", Pattern.compile("(?i)(secret[\"'\\s]*[:=][\"'\\s]*)([^\"'\\s,}]+)", Pattern.CASE_INSENSITIVE));
        SENSITIVE_VALUE_PATTERNS.put("authorization", Pattern.compile("(?i)(authorization[\"'\\s]*[:=][\"'\\s]*)([^\"'\\s,}]+)", Pattern.CASE_INSENSITIVE));
    }

    /**
     * 对数据进行脱敏处理
     * 
     * @param data 需要脱敏的数据
     * @return 脱敏后的数据
     */
    public static Object sanitizeData(Object data) {
        if (data == null) {
            return null;
        }

        try {
            if (data instanceof String) {
                return sanitizeString((String) data);
            } else if (data instanceof Map) {
                return sanitizeMap((Map<?, ?>) data);
            } else {
                // 尝试将对象转换为JSON进行脱敏
                String jsonString = objectMapper.writeValueAsString(data);
                return sanitizeJsonString(jsonString);
            }
        } catch (Exception e) {
            log.debug("数据脱敏处理异常，返回原始数据类型: {}", data.getClass().getSimpleName(), e);
            return data.toString();
        }
    }

    /**
     * 对字符串进行脱敏处理
     */
    private static String sanitizeString(String str) {
        if (str == null || str.trim().isEmpty()) {
            return str;
        }

        // 尝试解析为JSON进行脱敏
        if (isJsonString(str)) {
            return sanitizeJsonString(str);
        }

        // 对于非JSON字符串，进行简单的敏感信息替换
        return sanitizeSimpleString(str);
    }

    /**
     * 对Map进行脱敏处理
     */
    private static Map<String, Object> sanitizeMap(Map<?, ?> map) {
        Map<String, Object> sanitizedMap = new HashMap<>();
        
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            String key = String.valueOf(entry.getKey());
            Object value = entry.getValue();
            
            if (isSensitiveField(key)) {
                sanitizedMap.put(key, MASK_VALUE);
            } else {
                sanitizedMap.put(key, sanitizeData(value));
            }
        }
        
        return sanitizedMap;
    }

    /**
     * 对JSON字符串进行脱敏处理
     */
    private static String sanitizeJsonString(String jsonString) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);
            JsonNode sanitizedNode = sanitizeJsonNode(rootNode);
            return objectMapper.writeValueAsString(sanitizedNode);
        } catch (JsonProcessingException e) {
            log.debug("JSON脱敏处理失败，进行简单字符串脱敏", e);
            return sanitizeSimpleString(jsonString);
        }
    }

    /**
     * 对JsonNode进行脱敏处理
     */
    private static JsonNode sanitizeJsonNode(JsonNode node) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            ObjectNode sanitizedNode = objectMapper.createObjectNode();
            
            objectNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                JsonNode fieldValue = entry.getValue();
                
                if (isSensitiveField(fieldName)) {
                    sanitizedNode.put(fieldName, MASK_VALUE);
                } else {
                    sanitizedNode.set(fieldName, sanitizeJsonNode(fieldValue));
                }
            });
            
            return sanitizedNode;
        } else if (node.isArray()) {
            // 对于数组节点，递归处理每个元素
            for (int i = 0; i < node.size(); i++) {
                JsonNode arrayElement = node.get(i);
                sanitizeJsonNode(arrayElement);
            }
        }
        
        return node;
    }

    /**
     * 对简单字符串进行脱敏处理
     */
    private static String sanitizeSimpleString(String str) {
        if (str == null || str.trim().isEmpty()) {
            return str;
        }
        
        String result = str;
        
        // 1. 脱敏URL参数
        result = sanitizeUrlParameters(result);
        
        // 2. 脱敏敏感数据值模式
        result = sanitizeSensitiveValuePatterns(result);
        
        // 3. 脱敏个人信息
        result = sanitizePersonalInfo(result);
        
        // 4. 对于包含敏感关键词的字符串进行部分遮蔽
        result = sanitizeSensitiveKeywords(result);
        
        return result;
    }
    
    /**
     * 脱敏URL参数
     */
    private static String sanitizeUrlParameters(String str) {
        return URL_PARAM_PATTERN.matcher(str).replaceAll("$1" + MASK_VALUE);
    }
    
    /**
     * 脱敏敏感数据值模式
     */
    private static String sanitizeSensitiveValuePatterns(String str) {
        String result = str;
        for (Pattern pattern : SENSITIVE_VALUE_PATTERNS.values()) {
            result = pattern.matcher(result).replaceAll("$1" + MASK_VALUE);
        }
        return result;
    }
    
    /**
     * 脱敏个人信息
     */
    private static String sanitizePersonalInfo(String str) {
        String result = str;
        
        // 脱敏邮箱
        result = EMAIL_PATTERN.matcher(result).replaceAll("$1" + MASK_VALUE + "@$2");
        
        // 脱敏手机号
        result = PHONE_PATTERN.matcher(result).replaceAll("$1****$3");
        
        // 脱敏身份证号
        result = ID_CARD_PATTERN.matcher(result).replaceAll("$1********$3");
        
        // 脱敏银行卡号
        result = BANK_CARD_PATTERN.matcher(result).replaceAll("$1****$3");
        
        return result;
    }
    
    /**
     * 脱敏敏感关键词
     */
    private static String sanitizeSensitiveKeywords(String str) {
        String lowerStr = str.toLowerCase();
        
        for (String sensitiveField : JobConstants.SensitiveFields.FIELD_NAMES) {
            if (lowerStr.contains(sensitiveField.toLowerCase())) {
                // 如果字符串长度较短，直接返回遮蔽值
                if (str.length() <= 10) {
                    return MASK_VALUE;
                }
                // 对于较长的字符串，保留前后部分，中间用***替代
                return str.substring(0, 3) + MASK_VALUE + str.substring(str.length() - 3);
            }
        }
        
        return str;
    }

    /**
     * 判断字段名是否为敏感字段
     */
    private static boolean isSensitiveField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        return SENSITIVE_FIELD_PATTERN.matcher(fieldName).matches();
    }

    /**
     * 判断字符串是否为JSON格式
     */
    private static boolean isJsonString(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = str.trim();
        return (trimmed.startsWith("{") && trimmed.endsWith("}")) || 
               (trimmed.startsWith("[") && trimmed.endsWith("]"));
    }

    /**
     * 格式化执行时长
     */
    public static String formatDuration(long durationMs) {
        if (durationMs < 1000) {
            return durationMs + "ms";
        } else if (durationMs < 60000) {
            return String.format("%.2fs", durationMs / 1000.0);
        } else {
            long minutes = durationMs / 60000;
            long seconds = (durationMs % 60000) / 1000;
            return String.format("%dm%ds", minutes, seconds);
        }
    }

    /**
     * 截断长字符串
     */
    public static String truncateString(String str, int maxLength) {
        if (str == null || str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength) + "...";
    }

    /**
     * 安全地转换对象为字符串
     */
    public static String safeToString(Object obj) {
        if (obj == null) {
            return "null";
        }
        
        try {
            return obj.toString();
        } catch (Exception e) {
            return obj.getClass().getSimpleName() + "@" + Integer.toHexString(obj.hashCode());
        }
    }
    
    /**
     * 深度脱敏处理（递归处理嵌套对象）
     */
    public static Object deepSanitize(Object data, int maxDepth) {
        return deepSanitizeInternal(data, maxDepth, 0, new HashSet<>());
    }
    
    /**
     * 深度脱敏内部实现
     */
    private static Object deepSanitizeInternal(Object data, int maxDepth, int currentDepth, Set<Object> visited) {
        if (data == null || currentDepth >= maxDepth) {
            return data;
        }
        
        // 防止循环引用
        if (visited.contains(data)) {
            return "[CIRCULAR_REFERENCE]";
        }
        
        visited.add(data);
        
        try {
            if (data instanceof String) {
                return sanitizeString((String) data);
            } else if (data instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) data;
                Map<Object, Object> sanitizedMap = new HashMap<>();
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    Object key = entry.getKey();
                    Object value = entry.getValue();
                    
                    if (key instanceof String && isSensitiveField((String) key)) {
                        sanitizedMap.put(key, MASK_VALUE);
                    } else {
                        sanitizedMap.put(key, deepSanitizeInternal(value, maxDepth, currentDepth + 1, visited));
                    }
                }
                return sanitizedMap;
            } else if (data instanceof Collection) {
                Collection<?> collection = (Collection<?>) data;
                List<Object> sanitizedList = new ArrayList<>();
                for (Object item : collection) {
                    sanitizedList.add(deepSanitizeInternal(item, maxDepth, currentDepth + 1, visited));
                }
                return sanitizedList;
            } else if (data.getClass().isArray()) {
                Object[] array = (Object[]) data;
                Object[] sanitizedArray = new Object[array.length];
                for (int i = 0; i < array.length; i++) {
                    sanitizedArray[i] = deepSanitizeInternal(array[i], maxDepth, currentDepth + 1, visited);
                }
                return sanitizedArray;
            } else {
                // 对于其他对象类型，转换为字符串后脱敏
                return sanitizeString(data.toString());
            }
        } finally {
            visited.remove(data);
        }
    }
    
    /**
     * 验证字符串是否包含敏感信息
     */
    public static boolean containsSensitiveInfo(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        
        String lowerStr = str.toLowerCase();
        
        // 检查敏感关键词
        for (String sensitiveField : JobConstants.SensitiveFields.FIELD_NAMES) {
            if (lowerStr.contains(sensitiveField.toLowerCase())) {
                return true;
            }
        }
        
        // 检查敏感数据模式
        for (Pattern pattern : SENSITIVE_VALUE_PATTERNS.values()) {
            if (pattern.matcher(str).find()) {
                return true;
            }
        }
        
        // 检查个人信息模式
        return EMAIL_PATTERN.matcher(str).find() ||
               PHONE_PATTERN.matcher(str).find() ||
               ID_CARD_PATTERN.matcher(str).find() ||
               BANK_CARD_PATTERN.matcher(str).find();
    }
    
    /**
     * 获取脱敏统计信息
     */
    public static Map<String, Object> getSanitizationStats(String originalData, String sanitizedData) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("originalLength", originalData != null ? originalData.length() : 0);
        stats.put("sanitizedLength", sanitizedData != null ? sanitizedData.length() : 0);
        stats.put("containsSensitiveInfo", containsSensitiveInfo(originalData));
        stats.put("sanitizationApplied", !Objects.equals(originalData, sanitizedData));
        
        if (originalData != null && sanitizedData != null) {
            int maskCount = sanitizedData.length() - sanitizedData.replace(MASK_VALUE, "").length();
            stats.put("maskCount", maskCount / MASK_VALUE.length());
        }
        
        return stats;
    }
    
    /**
     * 创建安全的日志上下文
     */
    public static Map<String, Object> createSecureLogContext(String jobName, String executionId, 
                                                            Map<String, Object> additionalContext) {
        Map<String, Object> context = new HashMap<>();
        context.put("jobName", jobName);
        context.put("executionId", executionId);
        context.put("timestamp", System.currentTimeMillis());
        context.put("threadName", Thread.currentThread().getName());
        
        if (additionalContext != null) {
            for (Map.Entry<String, Object> entry : additionalContext.entrySet()) {
                context.put(entry.getKey(), sanitizeData(entry.getValue()));
            }
        }
        
        return context;
    }
}