package com.xylink.manager.job.util;

import org.slf4j.MDC;

/**
 * Job执行MDC上下文管理工具类
 * 用于在Job执行过程中管理executionId和jobName，确保日志的可追踪性
 */
public class JobMDCUtil {
    
    /**
     * MDC中executionId的key
     */
    public static final String EXECUTION_ID_KEY = "executionId";
    
    /**
     * MDC中jobName的key
     */
    public static final String JOB_NAME_KEY = "jobName";
    
    /**
     * 设置Job执行上下文
     * 
     * @param jobName 任务名称
     * @param executionId 执行ID
     */
    public static void setJobContext(String jobName, String executionId) {
        MDC.put(JOB_NAME_KEY, jobName);
        MDC.put(EXECUTION_ID_KEY, executionId);
    }
    
    /**
     * 清理Job执行上下文
     */
    public static void clearJobContext() {
        MDC.remove(JOB_NAME_KEY);
        MDC.remove(EXECUTION_ID_KEY);
    }
    
    /**
     * 获取当前执行ID
     * 
     * @return 当前执行ID，如果不存在则返回null
     */
    public static String getCurrentExecutionId() {
        return MDC.get(EXECUTION_ID_KEY);
    }
    
    /**
     * 获取当前Job名称
     * 
     * @return 当前Job名称，如果不存在则返回null
     */
    public static String getCurrentJobName() {
        return MDC.get(JOB_NAME_KEY);
    }
}