package com.xylink.manager.job.handler;

import com.xylink.manager.job.model.JobContext;
import com.xylink.manager.job.model.JobExecutionResult;
import com.xylink.manager.job.model.JobType;
import com.xylink.manager.job.exception.JobExecutionException;

/**
 * Job处理器接口
 * 定义了不同类型Job的执行逻辑
 */
public interface JobHandler {
    
    /**
     * 执行Job
     * 
     * @param context Job执行上下文
     * @return Job执行结果
     * @throws JobExecutionException Job执行异常
     */
    JobExecutionResult execute(JobContext context) throws JobExecutionException;
    
    /**
     * 获取支持的Job类型
     * 
     * @return 支持的Job类型
     */
    JobType getSupportedType();
    
    /**
     * 判断是否支持指定的Job类型
     * 
     * @param jobType Job类型
     * @return 是否支持
     */
    default boolean supports(JobType jobType) {
        return getSupportedType().equals(jobType);
    }
}