package com.xylink.manager.job.executor;

import com.xylink.manager.job.constant.JobConstants;
import com.xylink.manager.job.exception.JobExecutionException;
import com.xylink.manager.job.handler.JobHandler;
import com.xylink.manager.job.lock.JobDistributedLock;
import com.xylink.manager.job.metrics.JobMetrics;
import com.xylink.manager.job.model.*;
import com.xylink.manager.job.service.JobStatusService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Job执行器核心组件
 * 负责Job的执行调度、分布式锁管理、上下文隔离和状态管理
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class JobExecutor {

    @Autowired
    private JobDistributedLock distributedLock;

    @Autowired
    private JobStatusService jobStatusService;

    @Autowired
    private JobMetrics jobMetrics;

    @Autowired
    private List<JobHandler> jobHandlers;

    /**
     * Job处理器映射表
     */
    private Map<JobType, JobHandler> handlerMap;

    /**
     * 独立的线程池，确保每个Job在独立线程中执行
     */
    private ThreadPoolTaskExecutor jobExecutorPool;

    /**
     * 正在执行的Job上下文映射，用于优雅停机
     */
    private final Map<String, JobContext> runningJobs = new ConcurrentHashMap<>();

    /**
     * 日期时间格式化器
     */
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @PostConstruct
    public void init() {
        // 初始化Job处理器映射表
        handlerMap = jobHandlers.stream()
                .collect(Collectors.toMap(JobHandler::getSupportedType, handler -> handler));

        // 初始化独立的线程池
        jobExecutorPool = new ThreadPoolTaskExecutor();
        jobExecutorPool.setCorePoolSize(5);
        jobExecutorPool.setMaxPoolSize(20);
        jobExecutorPool.setQueueCapacity(100);
        jobExecutorPool.setThreadNamePrefix("job-executor-");
        jobExecutorPool.setRejectedExecutionHandler((r, executor) -> {
            log.error("Job执行线程池队列已满，拒绝执行任务");
        });
        jobExecutorPool.initialize();

        log.info("JobExecutor初始化完成，线程池配置: corePoolSize={}, maxPoolSize={}, queueCapacity={}",
                jobExecutorPool.getCorePoolSize(), jobExecutorPool.getMaxPoolSize(), jobExecutorPool.getQueueCapacity());
    }

    @PreDestroy
    public void destroy() {
        if (jobExecutorPool != null) {
            log.info("开始关闭Job执行线程池...");
            jobExecutorPool.shutdown();
            try {
                // 等待正在执行的任务完成，最多等待30秒
                if (!jobExecutorPool.getThreadPoolExecutor().awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("Job执行线程池未能在30秒内正常关闭，强制关闭");
                    jobExecutorPool.getThreadPoolExecutor().shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("等待Job执行线程池关闭被中断", e);
                jobExecutorPool.getThreadPoolExecutor().shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("Job执行线程池已关闭");
        }
    }

    /**
     * 异步执行Job
     * 
     * @param jobConfig Job配置
     */
    public void executeJobAsync(JobConfig jobConfig) {
        if (jobConfig == null || !jobConfig.isEnabled()) {
            log.debug("Job配置为空或已禁用，跳过执行: {}", jobConfig != null ? jobConfig.getJobName() : "null");
            return;
        }

        // 提交到独立线程池异步执行
        CompletableFuture.runAsync(() -> executeJob(jobConfig), jobExecutorPool)
                .exceptionally(throwable -> {
                    log.error("Job异步执行异常: jobName={}", jobConfig.getJobName(), throwable);
                    return null;
                });
    }

    /**
     * 同步执行Job（主要用于手动触发）
     * 
     * @param jobConfig Job配置
     */
    public void executeJob(JobConfig jobConfig) {
        if (jobConfig == null || !jobConfig.isEnabled()) {
            log.debug("Job配置为空或已禁用，跳过执行: {}", jobConfig != null ? jobConfig.getJobName() : "null");
            return;
        }

        String jobName = jobConfig.getJobName();
        String executionId = generateExecutionId();
        
        // 创建Job执行上下文
        JobContext context = new JobContext(executionId, jobConfig);
        context.setExecutionNode(getCurrentNodeId());
        
        // 记录正在执行的Job
        runningJobs.put(executionId, context);
        
        try {
            // 设置Job执行上下文
            setupJobExecutionContext(jobName, executionId);
            
            log.info("开始执行Job: jobName={}, executionId={}", jobName, executionId);
            
            // 尝试获取分布式锁
            boolean lockAcquired = acquireDistributedLock(jobName);
            
            // 记录锁获取指标
            jobMetrics.recordLockAcquisition(jobName, lockAcquired);
            
            if (!lockAcquired) {
                log.info("未能获取分布式锁，跳过执行: jobName={}, executionId={}", jobName, executionId);
                return;
            }
            
            try {
                // 记录Job开始执行
                jobStatusService.recordJobStart(jobName, executionId);
                
                // 执行Job并处理重试
                JobExecutionResult result = executeJobWithRetry(context);
                
                // 记录Job执行完成
                jobStatusService.recordJobFinish(jobName, executionId, result);
                
                // 记录执行指标
                String status = result.isSuccess() ? "success" : "failure";
                jobMetrics.recordJobExecution(jobName, jobConfig.getType(), status);
                
                if (result.getDurationMs() != null && result.getDurationMs() > 0) {
                    jobMetrics.recordJobDuration(jobName, jobConfig.getType(), 
                            java.time.Duration.ofMillis(result.getDurationMs()));
                }
                
                if (result.isSuccess()) {
                    log.info("Job执行成功: jobName={}, executionId={}, duration={}ms", 
                            jobName, executionId, result.getDurationMs());
                } else {
                    log.error("Job执行失败: jobName={}, executionId={}, error={}", 
                            jobName, executionId, result.getErrorMessage());
                    
                    // 检查是否需要告警
                    checkAndLogAlert(jobName, result);
                }
                
            } finally {
                // 释放分布式锁
                releaseDistributedLock(jobName);
            }
            
        } catch (Exception e) {
            log.error("Job执行过程中发生未预期异常: jobName={}, executionId={}", jobName, executionId, e);
            
            // 创建失败结果并记录
            JobExecutionResult failureResult = JobExecutionResult.failure(
                    0L,
                    "执行过程中发生未预期异常: " + e.getMessage(),
                    null,
                    context.getCurrentRetryCount()
            );
            
            try {
                jobStatusService.recordJobFinish(jobName, executionId, failureResult);
                
                // 记录失败指标
                jobMetrics.recordJobExecution(jobName, jobConfig.getType(), "failure");
            } catch (Exception recordException) {
                log.error("记录Job执行失败状态时发生异常: jobName={}, executionId={}", jobName, executionId, recordException);
            }
            
        } finally {
            // 清理执行上下文
            cleanupJobExecutionContext();
            
            // 移除正在执行的Job记录
            runningJobs.remove(executionId);
        }
    }

    /**
     * 执行Job并处理重试逻辑
     */
    private JobExecutionResult executeJobWithRetry(JobContext context) {
        String jobName = context.getJobName();
        String executionId = context.getExecutionId();
        JobConfig jobConfig = context.getJobConfig();
        
        JobExecutionResult lastResult = null;
        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(dateTimeFormatter);
        
        // 执行主逻辑和重试
        for (int attempt = 0; attempt <= jobConfig.getRetryCount(); attempt++) {
            try {
                if (attempt > 0) {
                    // 重试前等待
                    log.info("Job重试等待: jobName={}, executionId={}, attempt={}, waitSeconds={}", 
                            jobName, executionId, attempt, jobConfig.getRetryIntervalSeconds());
                    
                    Thread.sleep(jobConfig.getRetryIntervalSeconds() * 1000L);
                    context.incrementRetryCount();
                    
                    log.info("开始Job重试: jobName={}, executionId={}, attempt={}/{}", 
                            jobName, executionId, attempt, jobConfig.getRetryCount());
                }
                
                // 获取对应的Job处理器
                JobHandler handler = handlerMap.get(jobConfig.getType());
                if (handler == null) {
                    throw new JobExecutionException(jobName, executionId, 
                            "未找到对应的Job处理器: " + jobConfig.getType());
                }
                
                // 执行Job
                JobExecutionResult result = handler.execute(context);
                
                if (result.isSuccess()) {
                    // 执行成功，返回结果
                    long endTime = System.currentTimeMillis();
                    
                    return JobExecutionResult.success(
                            endTime - startTime,
                            result.getDetails()
                    );
                } else {
                    // 执行失败，记录结果但继续重试
                    lastResult = result;
                    log.warn("Job执行失败，准备重试: jobName={}, executionId={}, attempt={}/{}, error={}", 
                            jobName, executionId, attempt, jobConfig.getRetryCount(), result.getErrorMessage());
                }
                
            } catch (InterruptedException e) {
                log.warn("Job执行被中断: jobName={}, executionId={}, attempt={}", jobName, executionId, attempt, e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("Job执行异常: jobName={}, executionId={}, attempt={}/{}", 
                        jobName, executionId, attempt, jobConfig.getRetryCount(), e);
                
                // 创建失败结果
                long endTime = System.currentTimeMillis();
                
                lastResult = JobExecutionResult.failure(
                        endTime - startTime,
                        e.getMessage(),
                        null,
                        context.getCurrentRetryCount()
                );
            }
        }
        
        // 所有重试都失败了，返回最后一次的失败结果
        if (lastResult == null) {
            long endTime = System.currentTimeMillis();
            
            lastResult = JobExecutionResult.failure(
                    endTime - startTime,
                    "Job执行失败，已达到最大重试次数",
                    null,
                    context.getCurrentRetryCount()
            );
        }
        
        log.error("Job执行最终失败: jobName={}, executionId={}, totalRetries={}, error={}", 
                jobName, executionId, context.getCurrentRetryCount(), lastResult.getErrorMessage());
        
        return lastResult;
    }

    /**
     * 获取分布式锁
     */
    private boolean acquireDistributedLock(String jobName) {
        try {
            boolean acquired = distributedLock.tryLock(jobName);
            log.debug("分布式锁获取结果: jobName={}, acquired={}", jobName, acquired);
            return acquired;
        } catch (Exception e) {
            log.error("获取分布式锁异常: jobName={}", jobName, e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     */
    private void releaseDistributedLock(String jobName) {
        try {
            distributedLock.releaseLock(jobName);
            log.debug("分布式锁已释放: jobName={}", jobName);
        } catch (Exception e) {
            log.error("释放分布式锁异常: jobName={}", jobName, e);
        }
    }

    /**
     * 设置Job执行上下文
     */
    private void setupJobExecutionContext(String jobName, String executionId) {
        // 清理之前的MDC上下文（防止上下文泄露）
        MDC.clear();
        
        // 设置当前Job的MDC上下文
        MDC.put(JobConstants.MDC_JOB_NAME, jobName);
        MDC.put(JobConstants.MDC_EXECUTION_ID, executionId);
        
        log.debug("Job执行上下文已设置: jobName={}, executionId={}", jobName, executionId);
    }

    /**
     * 清理Job执行上下文
     */
    private void cleanupJobExecutionContext() {
        // 清理MDC上下文，防止Job间的数据泄露
        MDC.clear();
        log.debug("Job执行上下文已清理");
    }

    /**
     * 生成执行ID
     */
    private String generateExecutionId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取当前节点ID
     */
    private String getCurrentNodeId() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            log.warn("获取主机名失败，使用默认值", e);
            return "unknown-node";
        }
    }

    /**
     * 检查并记录告警日志
     */
    private void checkAndLogAlert(String jobName, JobExecutionResult result) {
        try {
            JobStatus status = jobStatusService.getJobStatus(jobName);
            if (status != null && status.getConsecutiveFailCount() != null) {
                int consecutiveFailCount = status.getConsecutiveFailCount();
                JobConfig jobConfig = runningJobs.values().stream()
                        .filter(ctx -> jobName.equals(ctx.getJobName()))
                        .map(JobContext::getJobConfig)
                        .findFirst()
                        .orElse(null);
                
                if (jobConfig != null && consecutiveFailCount >= jobConfig.getAlertThreshold()) {
                    log.error("JOB_ALERT: Job连续失败达到告警阈值: jobName={}, consecutiveFailCount={}, alertThreshold={}, lastError={}", 
                            jobName, consecutiveFailCount, jobConfig.getAlertThreshold(), result.getErrorMessage());
                }
            }
        } catch (Exception e) {
            log.error("检查Job告警状态异常: jobName={}", jobName, e);
        }
    }

    /**
     * 获取当前正在执行的Job数量
     */
    public int getRunningJobCount() {
        return runningJobs.size();
    }

    /**
     * 获取线程池状态信息
     */
    public String getThreadPoolStatus() {
        if (jobExecutorPool == null) {
            return "线程池未初始化";
        }
        
        return String.format("线程池状态: 核心线程数=%d, 最大线程数=%d, 当前线程数=%d, 活跃线程数=%d, 队列大小=%d",
                jobExecutorPool.getCorePoolSize(),
                jobExecutorPool.getMaxPoolSize(),
                jobExecutorPool.getThreadPoolExecutor().getPoolSize(),
                jobExecutorPool.getActiveCount(),
                jobExecutorPool.getThreadPoolExecutor().getQueue().size());
    }

    /**
     * 强制释放指定Job的分布式锁（紧急情况使用）
     */
    public void forceReleaseLock(String jobName) {
        try {
            distributedLock.forceReleaseLock(jobName);
            log.warn("强制释放Job分布式锁: jobName={}", jobName);
        } catch (Exception e) {
            log.error("强制释放Job分布式锁异常: jobName={}", jobName, e);
        }
    }

    /**
     * 获取正在运行的Job上下文映射（用于优雅停机）
     */
    public Map<String, JobContext> getRunningJobContexts() {
        return new ConcurrentHashMap<>(runningJobs);
    }
}