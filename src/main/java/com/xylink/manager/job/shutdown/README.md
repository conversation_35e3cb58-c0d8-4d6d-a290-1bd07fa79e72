# Job优雅停机机制

## 概述

Job优雅停机机制确保在应用关闭时，正在执行的任务能够正常完成，分布式锁能够正确释放，执行状态能够准确记录。

## 核心组件

### JobGracefulShutdownHandler

优雅停机处理器，实现了`ApplicationListener<ContextClosedEvent>`接口，监听应用停机信号。

#### 主要功能

1. **监听停机信号**：监听Spring应用上下文关闭事件
2. **等待任务完成**：等待正在执行的Job在超时时间内完成
3. **资源清理**：强制清理未完成的任务资源
4. **状态记录**：记录强制停机的任务状态

#### 配置参数

- `job.shutdown.timeout`：优雅停机超时时间（秒），默认60秒

## 停机流程

### 1. 停止调度新任务

- 检查JobScheduler状态
- 获取当前正在运行的Job信息
- 停止调度新的任务

### 2. 等待正在执行的任务完成

- 创建CountDownLatch等待信号
- 启动监控线程检查任务完成状态
- 在超时时间内等待所有任务完成

### 3. 强制清理剩余资源

如果超时后仍有未完成的任务：

- 清理正在运行的Job记录
- 释放可能残留的分布式锁
- 记录强制停机状态

## 使用方式

### 自动启用

优雅停机处理器会自动注册为Spring Bean，无需手动配置。

### 配置超时时间

在`application.properties`中配置：

```properties
# Job优雅停机超时时间（秒）
job.shutdown.timeout=60
```

### 监控停机状态

可以通过以下方法监控停机状态：

```java
@Autowired
private JobGracefulShutdownHandler shutdownHandler;

// 检查是否正在停机
boolean isShuttingDown = shutdownHandler.isShutdownInProgress();

// 获取等待完成的Job数量
int waitingCount = shutdownHandler.getWaitingJobCount();

// 获取配置的超时时间
int timeoutSeconds = shutdownHandler.getShutdownTimeoutSeconds();
```

## 日志输出

优雅停机过程中会输出详细的日志信息：

```
INFO  - 接收到应用停机信号，开始Job优雅停机流程...
INFO  - 正在停止Job调度...
INFO  - Job调度停止完成，当前正在执行的Job数量: 2
INFO  - 等待2个正在执行的Job完成，最大等待时间: 60秒
INFO  - 检测到Job已完成: jobName=sync-data, executionId=abc123
INFO  - 所有正在执行的Job已完成
INFO  - Job优雅停机完成，总耗时: 15000ms
```

如果发生超时：

```
WARN  - 等待Job完成超时，剩余未完成的Job数量: 1
WARN  - 以下Job在停机超时后仍未完成:
WARN  - - jobName=long-running-job, executionId=def456, executionNode=node-1
INFO  - 开始强制清理剩余资源...
WARN  - 清理1个未完成的Job记录
WARN  - 已记录Job强制停机状态: jobName=long-running-job, executionId=def456
```

## 最佳实践

### 1. 合理设置超时时间

根据Job的平均执行时间设置合理的超时时间：

- 短任务（几秒到几分钟）：设置30-60秒
- 长任务（几分钟到几十分钟）：设置120-300秒
- 超长任务：考虑优化任务逻辑或分解任务

### 2. 任务设计考虑

- **幂等性**：确保任务可以安全重试
- **检查点**：长时间任务应该支持中断和恢复
- **资源清理**：任务应该正确清理临时资源

### 3. 监控和告警

- 监控停机时间，如果经常超时需要优化
- 设置告警，当强制停机发生时及时通知
- 定期检查强制停机的任务是否需要手动处理

## 故障处理

### 常见问题

1. **停机超时**
   - 检查是否有长时间运行的任务
   - 考虑增加超时时间或优化任务逻辑

2. **锁未释放**
   - 检查Redis连接是否正常
   - 手动清理残留的锁

3. **状态不一致**
   - 检查Redis中的任务状态
   - 必要时手动修正状态

### 手动清理

如果需要手动清理资源：

```bash
# 清理Redis中的锁
redis-cli DEL job_lock:task-name

# 清理正在运行的任务记录
redis-cli DEL job_running:task-name

# 检查任务状态
redis-cli HGETALL job_status:task-name
```

## 测试

### 单元测试

- `JobGracefulShutdownHandlerTest`：基本功能测试
- `JobGracefulShutdownSimpleTest`：简单属性测试

### 集成测试

- `JobGracefulShutdownIntegrationTest`：完整流程测试

### 手动测试

1. 启动应用并触发长时间运行的Job
2. 发送停机信号（SIGTERM）
3. 观察日志输出和停机行为
4. 验证任务状态和锁释放情况

## 相关需求

本实现满足以下需求：

- **需求9.1**：应用接收到停机信号时等待当前正在执行的任务完成
- **需求9.2**：任务在停机超时时间内完成时确保分布式锁被正确释放
- **需求9.3**：任务在停机超时时间内完成时确保执行状态被正确记录
- **需求9.4**：停机超时时间到达时强制终止剩余任务并清理资源