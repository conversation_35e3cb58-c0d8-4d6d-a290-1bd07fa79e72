package com.xylink.manager.job.shutdown;

import com.xylink.manager.job.config.JobConfigLoader;
import com.xylink.manager.job.executor.JobExecutor;
import com.xylink.manager.job.lock.JobDistributedLock;
import com.xylink.manager.job.model.*;
import com.xylink.manager.job.scheduler.JobScheduler;
import com.xylink.manager.job.service.JobStatusService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Job优雅停机处理器
 * 负责监听应用停机信号，等待正在执行的任务完成，确保锁的正确释放和状态记录
 */
@Slf4j
@Component
public class JobGracefulShutdownHandler implements ApplicationListener<ContextClosedEvent> {

    @Autowired
    private JobScheduler jobScheduler;

    @Autowired
    private JobExecutor jobExecutor;

    @Autowired
    private JobDistributedLock distributedLock;

    @Autowired
    private JobStatusService jobStatusService;

    @Autowired
    private JobConfigLoader jobConfigLoader;

    /**
     * 优雅停机超时时间（秒），从jobs.yaml配置中获取
     */
    private int shutdownTimeoutSeconds = 60;

    /**
     * 停机状态标志
     */
    private final AtomicBoolean shutdownInProgress = new AtomicBoolean(false);

    /**
     * 正在执行的Job上下文映射
     */
    private final Map<String, JobContext> runningJobs = new ConcurrentHashMap<>();

    /**
     * 停机完成信号
     */
    private CountDownLatch shutdownLatch;

    /**
     * 日期时间格式化器
     */
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        if (shutdownInProgress.compareAndSet(false, true)) {
            log.info("接收到应用停机信号，开始Job优雅停机流程...");
            
            try {
                // 从配置中获取停机超时时间
                loadShutdownTimeout();
                performGracefulShutdown();
            } catch (Exception e) {
                log.error("Job优雅停机过程中发生异常", e);
            }
            
            log.info("Job优雅停机流程完成");
        }
    }

    /**
     * 从配置中加载停机超时时间
     */
    private void loadShutdownTimeout() {
        try {
            JobsConfig jobsConfig = jobConfigLoader.loadJobsConfig();
            if (jobsConfig.getSystem() != null) {
                this.shutdownTimeoutSeconds = jobsConfig.getSystem().getShutdownTimeoutSeconds();
                log.info("从配置中加载停机超时时间: {}秒", shutdownTimeoutSeconds);
            }
        } catch (Exception e) {
            log.warn("加载停机超时配置失败，使用默认值: {}秒", shutdownTimeoutSeconds, e);
        }
    }

    /**
     * 执行优雅停机流程
     */
    private void performGracefulShutdown() {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 停止调度新的任务
            stopJobScheduling();
            
            // 2. 等待正在执行的任务完成
            waitForRunningJobsToComplete();
            
            // 3. 强制清理剩余资源
            forceCleanupRemainingResources();
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("Job优雅停机完成，总耗时: {}ms", duration);
            
        } catch (Exception e) {
            log.error("Job优雅停机过程中发生异常", e);
            
            // 即使发生异常，也要尝试清理资源
            try {
                forceCleanupRemainingResources();
            } catch (Exception cleanupException) {
                log.error("强制清理资源时发生异常", cleanupException);
            }
        }
    }

    /**
     * 停止Job调度
     */
    private void stopJobScheduling() {
        try {
            log.info("正在停止Job调度...");
            
            // 检查调度器状态
            if (jobScheduler.isShutdown()) {
                log.info("Job调度器已经关闭");
                return;
            }
            
            // 获取当前正在运行的Job信息
            updateRunningJobsInfo();
            
            log.info("Job调度停止完成，当前正在执行的Job数量: {}", runningJobs.size());
            
        } catch (Exception e) {
            log.error("停止Job调度时发生异常", e);
        }
    }

    /**
     * 等待正在执行的任务完成
     */
    private void waitForRunningJobsToComplete() {
        if (runningJobs.isEmpty()) {
            log.info("没有正在执行的Job，跳过等待");
            return;
        }
        
        log.info("等待{}个正在执行的Job完成，最大等待时间: {}秒", runningJobs.size(), shutdownTimeoutSeconds);
        
        // 创建停机信号量
        shutdownLatch = new CountDownLatch(runningJobs.size());
        
        // 启动监控线程
        startJobCompletionMonitor();
        
        try {
            // 等待所有Job完成或超时
            boolean completed = shutdownLatch.await(shutdownTimeoutSeconds, TimeUnit.SECONDS);
            
            if (completed) {
                log.info("所有正在执行的Job已完成");
            } else {
                log.warn("等待Job完成超时，剩余未完成的Job数量: {}", shutdownLatch.getCount());
                logRemainingJobs();
            }
            
        } catch (InterruptedException e) {
            log.warn("等待Job完成被中断", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 启动Job完成监控线程
     */
    private void startJobCompletionMonitor() {
        Thread monitorThread = new Thread(() -> {
            try {
                while (shutdownLatch.getCount() > 0 && !Thread.currentThread().isInterrupted()) {
                    // 检查Job状态更新
                    checkJobCompletionStatus();
                    
                    // 每秒检查一次
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                log.debug("Job完成监控线程被中断");
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("Job完成监控线程异常", e);
            }
        }, "job-shutdown-monitor");
        
        monitorThread.setDaemon(true);
        monitorThread.start();
    }

    /**
     * 检查Job完成状态
     */
    private void checkJobCompletionStatus() {
        try {
            // 获取当前正在运行的Job
            List<RunningJobInfo> currentRunningJobs = jobStatusService.getRunningJobs();
            
            // 检查哪些Job已经完成
            runningJobs.entrySet().removeIf(entry -> {
                String executionId = entry.getKey();
                JobContext context = entry.getValue();
                
                // 检查该Job是否还在运行
                boolean stillRunning = currentRunningJobs.stream()
                        .anyMatch(runningJob -> executionId.equals(runningJob.getExecutionId()));
                
                if (!stillRunning) {
                    log.info("检测到Job已完成: jobName={}, executionId={}", 
                            context.getJobName(), executionId);
                    
                    // 减少等待计数
                    shutdownLatch.countDown();
                    return true; // 从映射中移除
                }
                
                return false; // 保留在映射中
            });
            
        } catch (Exception e) {
            log.error("检查Job完成状态时发生异常", e);
        }
    }

    /**
     * 强制清理剩余资源
     */
    private void forceCleanupRemainingResources() {
        log.info("开始强制清理剩余资源...");
        
        try {
            // 1. 清理剩余的正在运行Job记录
            cleanupRemainingRunningJobs();
            
            // 2. 释放可能残留的分布式锁
            releaseRemainingLocks();
            
            // 3. 记录强制停机状态
            recordForcedShutdownStatus();
            
            log.info("强制清理剩余资源完成");
            
        } catch (Exception e) {
            log.error("强制清理剩余资源时发生异常", e);
        }
    }

    /**
     * 清理剩余的正在运行Job记录
     */
    private void cleanupRemainingRunningJobs() {
        if (runningJobs.isEmpty()) {
            return;
        }
        
        log.warn("清理{}个未完成的Job记录", runningJobs.size());
        
        String currentTime = LocalDateTime.now().format(dateTimeFormatter);
        
        for (Map.Entry<String, JobContext> entry : runningJobs.entrySet()) {
            String executionId = entry.getKey();
            JobContext context = entry.getValue();
            String jobName = context.getJobName();
            
            try {
                // 创建强制停机的执行结果
                JobExecutionResult shutdownResult = JobExecutionResult.failure(
                        0L, // 无法准确计算执行时间
                        "应用停机时强制终止",
                        "FORCED_SHUTDOWN",
                        context.getCurrentRetryCount()
                );
                shutdownResult.setStartTime(currentTime);
                shutdownResult.setEndTime(currentTime);
                
                // 记录Job强制停机状态
                jobStatusService.recordJobFinish(jobName, executionId, shutdownResult);
                
                log.warn("已记录Job强制停机状态: jobName={}, executionId={}", jobName, executionId);
                
            } catch (Exception e) {
                log.error("清理Job记录时发生异常: jobName={}, executionId={}", jobName, executionId, e);
            }
        }
        
        runningJobs.clear();
    }

    /**
     * 释放可能残留的分布式锁
     */
    private void releaseRemainingLocks() {
        try {
            // 获取所有Job配置，尝试释放可能的锁
            List<JobConfig> jobConfigs = jobStatusService.getAllJobConfigs();
            
            for (JobConfig jobConfig : jobConfigs) {
                String jobName = jobConfig.getJobName();
                
                try {
                    // 尝试强制释放锁
                    distributedLock.forceReleaseLock(jobName);
                    log.debug("尝试释放Job锁: jobName={}", jobName);
                    
                } catch (Exception e) {
                    log.debug("释放Job锁时发生异常: jobName={}", jobName, e);
                }
            }
            
            log.info("分布式锁清理完成");
            
        } catch (Exception e) {
            log.error("释放剩余分布式锁时发生异常", e);
        }
    }

    /**
     * 记录强制停机状态
     */
    private void recordForcedShutdownStatus() {
        try {
            log.info("应用优雅停机完成，停机超时时间: {}秒", shutdownTimeoutSeconds);
            
        } catch (Exception e) {
            log.error("记录强制停机状态时发生异常", e);
        }
    }

    /**
     * 更新正在运行的Job信息
     */
    private void updateRunningJobsInfo() {
        try {
            // 从JobExecutor获取正在运行的Job上下文
            Map<String, JobContext> executorRunningJobs = jobExecutor.getRunningJobContexts();
            
            runningJobs.clear();
            runningJobs.putAll(executorRunningJobs);
            
            log.info("更新正在运行的Job信息完成，数量: {}", runningJobs.size());
            
        } catch (Exception e) {
            log.error("更新正在运行的Job信息时发生异常", e);
            
            // 如果从JobExecutor获取失败，尝试从Redis获取
            try {
                List<RunningJobInfo> runningJobInfos = jobStatusService.getRunningJobs();
                
                runningJobs.clear();
                
                for (RunningJobInfo runningJobInfo : runningJobInfos) {
                    // 创建简化的JobContext用于跟踪
                    JobContext context = new JobContext(
                            runningJobInfo.getExecutionId(),
                            null // 在停机阶段不需要完整的JobConfig
                    );
                    context.setJobName(runningJobInfo.getJobName());
                    context.setExecutionNode(runningJobInfo.getExecutionNode());
                    
                    runningJobs.put(runningJobInfo.getExecutionId(), context);
                }
                
                log.info("从Redis获取正在运行的Job信息完成，数量: {}", runningJobs.size());
                
            } catch (Exception redisException) {
                log.error("从Redis获取正在运行的Job信息也失败", redisException);
            }
        }
    }

    /**
     * 记录剩余未完成的Job
     */
    private void logRemainingJobs() {
        if (runningJobs.isEmpty()) {
            return;
        }
        
        log.warn("以下Job在停机超时后仍未完成:");
        for (Map.Entry<String, JobContext> entry : runningJobs.entrySet()) {
            String executionId = entry.getKey();
            JobContext context = entry.getValue();
            log.warn("- jobName={}, executionId={}, executionNode={}", 
                    context.getJobName(), executionId, context.getExecutionNode());
        }
    }

    /**
     * 检查是否正在停机
     */
    public boolean isShutdownInProgress() {
        return shutdownInProgress.get();
    }

    /**
     * 获取停机超时时间
     */
    public int getShutdownTimeoutSeconds() {
        return shutdownTimeoutSeconds;
    }

    /**
     * 获取当前等待完成的Job数量
     */
    public int getWaitingJobCount() {
        return shutdownLatch != null ? (int) shutdownLatch.getCount() : 0;
    }
}