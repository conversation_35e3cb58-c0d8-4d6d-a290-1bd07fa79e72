package com.xylink.manager.job.service;

import com.xylink.manager.job.config.JobConfigLoader;
import com.xylink.manager.job.exception.JobConfigException;
import com.xylink.manager.job.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 内存模式的Job状态服务
 * 用于Redis不可用时的单机模式降级
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class InMemoryJobStatusService {

    @Autowired
    private JobConfigLoader jobConfigLoader;

    /**
     * 存储Job状态信息
     */
    private final ConcurrentHashMap<String, JobStatus> jobStatusMap = new ConcurrentHashMap<>();

    /**
     * 存储正在运行的Job
     */
    private final ConcurrentHashMap<String, String> runningJobs = new ConcurrentHashMap<>();

    /**
     * 存储执行历史（每个Job最多保留100条记录）
     */
    private final ConcurrentHashMap<String, List<JobExecutionHistory>> executionHistoryMap = new ConcurrentHashMap<>();

    /**
     * 最大历史记录数
     */
    private static final int MAX_HISTORY_SIZE = 100;

    /**
     * 日期时间格式化器
     */
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 记录Job开始执行
     */
    public void recordJobStart(String jobName, String executionId) {
        try {
            String currentTime = LocalDateTime.now().format(dateTimeFormatter);
            String nodeId = getCurrentNodeId();

            // 更新Job状态
            JobStatus status = jobStatusMap.computeIfAbsent(jobName, k -> createDefaultJobStatus(jobName));
            status.setLastTriggerTime(currentTime);
            status.setLastExecutionNode(nodeId);
            status.setLastExecutionId(executionId);
            status.setRunning(true);

            // 记录正在运行的Job
            runningJobs.put(executionId, jobName);

            log.debug("记录Job开始执行(内存模式): jobName={}, executionId={}", jobName, executionId);
        } catch (Exception e) {
            log.error("记录Job开始执行失败(内存模式): jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 记录Job执行完成
     */
    public void recordJobCompletion(String jobName, String executionId, JobExecutionResult result) {
        try {
            String currentTime = LocalDateTime.now().format(dateTimeFormatter);
            String status = result.isSuccess() ? "SUCCESS" : "FAIL";

            // 更新Job状态
            JobStatus jobStatus = jobStatusMap.computeIfAbsent(jobName, k -> createDefaultJobStatus(jobName));
            jobStatus.setLastStatus(status);
            jobStatus.setLastFinishTime(currentTime);
            jobStatus.setLastDurationMs(result.getDurationMs());
            jobStatus.setRunning(false);

            if (result.isSuccess()) {
                jobStatus.setLastSuccessTime(currentTime);
                jobStatus.setConsecutiveFailCount(0);
            } else {
                // 增加连续失败计数
                Integer failCount = jobStatus.getConsecutiveFailCount();
                jobStatus.setConsecutiveFailCount(failCount != null ? failCount + 1 : 1);
            }

            // 删除正在运行的记录
            runningJobs.remove(executionId);

            // 添加到执行历史
            addToExecutionHistory(jobName, executionId, result);

            log.debug("记录Job执行完成(内存模式): jobName={}, executionId={}, status={}", jobName, executionId, status);
        } catch (Exception e) {
            log.error("记录Job执行完成失败(内存模式): jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 获取Job状态
     */
    public JobStatus getJobStatus(String jobName) {
        try {
            JobStatus status = jobStatusMap.get(jobName);
            if (status == null) {
                status = createDefaultJobStatus(jobName);
                jobStatusMap.put(jobName, status);
            }

            // 检查是否正在运行
            boolean isRunning = runningJobs.values().contains(jobName);
            status.setRunning(isRunning);

            return status;
        } catch (Exception e) {
            log.error("获取Job状态失败(内存模式): {}", jobName, e);
            return createDefaultJobStatus(jobName);
        }
    }

    /**
     * 获取所有Job状态
     */
    public List<JobStatus> getAllJobStatus() {
        try {
            List<JobConfig> configs = getAllJobConfigs();
            return configs.stream()
                    .map(config -> getJobStatus(config.getJobName()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取所有Job状态失败(内存模式)", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有生效的Job配置
     */
    public List<JobConfig> getAllJobConfigs() {
        try {
            return jobConfigLoader.loadJobConfigs().stream()
                    .filter(JobConfig::isEnabled)
                    .collect(Collectors.toList());
        } catch (JobConfigException e) {
            log.error("获取Job配置失败(内存模式)", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取Job执行历史
     */
    public List<JobExecutionHistory> getJobExecutionHistory(String jobName, int page, int size) {
        try {
            List<JobExecutionHistory> history = executionHistoryMap.getOrDefault(jobName, new ArrayList<>());
            
            // 计算分页
            int start = page * size;
            int end = Math.min(start + size, history.size());
            
            if (start >= history.size()) {
                return new ArrayList<>();
            }
            
            return new ArrayList<>(history.subList(start, end));
        } catch (Exception e) {
            log.error("获取Job执行历史失败(内存模式): jobName={}", jobName, e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建默认的Job状态
     */
    private JobStatus createDefaultJobStatus(String jobName) {
        JobStatus status = new JobStatus();
        status.setJobName(jobName);
        status.setRunning(false);
        status.setConsecutiveFailCount(0);
        
        // 尝试从配置中获取Job信息
        try {
            List<JobConfig> configs = jobConfigLoader.loadJobConfigs();
            configs.stream()
                    .filter(config -> jobName.equals(config.getJobName()))
                    .findFirst()
                    .ifPresent(config -> {
                        status.setType(config.getType());
                        status.setEnabled(config.isEnabled());
                        status.setCron(config.getCron());
                        status.setDescription(config.getDescription());
                    });
        } catch (Exception e) {
            log.debug("获取Job配置失败，使用默认状态: jobName={}", jobName);
        }
        
        return status;
    }

    /**
     * 添加到执行历史
     */
    private void addToExecutionHistory(String jobName, String executionId, JobExecutionResult result) {
        try {
            JobExecutionHistory history = new JobExecutionHistory();
            history.setJobName(jobName);
            history.setExecutionId(executionId);
            history.setExecutionNode(getCurrentNodeId());
            history.setStartTime(result.getStartTime());
            history.setFinishTime(result.getEndTime());
            history.setDurationMs(result.getDurationMs());
            history.setStatus(result.isSuccess() ? "SUCCESS" : "FAIL");
            history.setRetryCount(result.getRetryCount());
            history.setErrorMessage(result.getDetails());

            // 添加到历史列表头部（最新的记录在前面）
            List<JobExecutionHistory> historyList = executionHistoryMap.computeIfAbsent(jobName, k -> new ArrayList<>());
            historyList.add(0, history);

            // 保持列表大小不超过最大限制
            if (historyList.size() > MAX_HISTORY_SIZE) {
                historyList.subList(MAX_HISTORY_SIZE, historyList.size()).clear();
            }
        } catch (Exception e) {
            log.error("添加执行历史失败(内存模式): jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 获取当前节点ID
     */
    private String getCurrentNodeId() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            log.warn("获取主机名失败，使用默认值", e);
            return "unknown-node";
        }
    }

    /**
     * 清理所有数据（用于测试或重置）
     */
    public void clearAllData() {
        log.warn("清理所有内存状态数据");
        jobStatusMap.clear();
        runningJobs.clear();
        executionHistoryMap.clear();
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalJobs", jobStatusMap.size());
        stats.put("runningJobs", runningJobs.size());
        stats.put("totalHistoryRecords", executionHistoryMap.values().stream().mapToInt(List::size).sum());
        return stats;
    }
}
