package com.xylink.manager.job.service;

import com.xylink.manager.job.config.JobConfigLoader;
import com.xylink.manager.job.exception.JobConfigException;
import com.xylink.manager.job.model.JobConfig;
import com.xylink.manager.job.model.JobSystemConfig;
import com.xylink.manager.job.model.JobsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Job配置管理服务
 * 提供统一的配置访问接口
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class JobConfigService {

    @Autowired
    private JobConfigLoader jobConfigLoader;

    /**
     * 获取系统配置
     */
    public JobSystemConfig getSystemConfig() throws JobConfigException {
        JobsConfig jobsConfig = jobConfigLoader.loadJobsConfig();
        return jobsConfig.getSystem();
    }

    /**
     * 获取所有Job配置
     */
    public List<JobConfig> getJobConfigs() throws JobConfigException {
        return jobConfigLoader.loadJobConfigs();
    }

    /**
     * 获取完整的Jobs配置
     */
    public JobsConfig getJobsConfig() throws JobConfigException {
        return jobConfigLoader.loadJobsConfig();
    }

    /**
     * 获取指定Job的配置
     */
    public JobConfig getJobConfig(String jobName) throws JobConfigException {
        List<JobConfig> jobConfigs = getJobConfigs();
        return jobConfigs.stream()
                .filter(config -> jobName.equals(config.getJobName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 验证Job配置
     */
    public void validateJobConfig(JobConfig jobConfig) throws JobConfigException {
        jobConfigLoader.validateJobConfig(jobConfig);
    }

    /**
     * 验证Cron表达式
     */
    public void validateCronExpression(String cron) throws JobConfigException {
        jobConfigLoader.validateCronExpression(cron);
    }
}