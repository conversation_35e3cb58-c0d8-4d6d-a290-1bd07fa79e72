package com.xylink.manager.job.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.manager.job.config.JobConfigLoader;
import com.xylink.manager.job.config.RedisDebounceConfig;
import com.xylink.manager.job.constant.JobConstants;
import com.xylink.manager.job.exception.JobConfigException;
import com.xylink.manager.job.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Job状态管理服务
 * 负责Job状态的Redis存储和查询功能
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class JobStatusService {

    @Autowired(required = false)
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private JobConfigLoader jobConfigLoader;

    @Autowired
    private InMemoryJobStatusService inMemoryJobStatusService;

    @Autowired
    private RedisDebounceConfig redisDebounceConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final String currentNode = getCurrentNodeId();

    /**
     * Redis是否可用的标志
     */
    private volatile boolean redisAvailable = true;

    /**
     * 上次检查Redis可用性的时间
     */
    private volatile long lastRedisCheckTime = 0;

    /**
     * Redis连续可用次数（防抖动）
     */
    private final AtomicInteger redisConsecutiveSuccessCount = new AtomicInteger(0);

    /**
     * Redis连续失败次数（防抖动）
     */
    private final AtomicInteger redisConsecutiveFailureCount = new AtomicInteger(0);

    /**
     * 检查Redis是否可用（带防抖动机制）
     */
    private boolean isRedisAvailable() {
        if (stringRedisTemplate == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();

        // 如果Redis已经可用，定期检查稳定性
        if (redisAvailable) {
            // 每隔检查间隔验证Redis稳定性
            if (currentTime - lastRedisCheckTime < redisDebounceConfig.getRedisCheckInterval()) {
                return true;
            }

            lastRedisCheckTime = currentTime;

            try {
                stringRedisTemplate.hasKey(RedisDebounceConfig.HEALTH_CHECK_KEY);
                // Redis仍然可用，重置失败计数
                redisConsecutiveFailureCount.set(0);
                log.debug("Redis连接稳定性检查通过");
                return true;

            } catch (Exception e) {
                // Redis出现问题，增加失败计数
                int failureCount = redisConsecutiveFailureCount.incrementAndGet();
                log.warn("Redis连接检查失败 ({}/{}): {}", failureCount, redisDebounceConfig.getRequiredFailureCount(), e.getMessage());

                // 连续失败达到阈值，切换到内存模式
                if (failureCount >= redisDebounceConfig.getRequiredFailureCount()) {
                    redisAvailable = false;
                    redisConsecutiveSuccessCount.set(0);
                    log.warn("Redis连续失败{}次，状态服务切换到内存模式", failureCount);
                }
                return false;
            }
        }

        // Redis不可用，检查是否恢复
        if (currentTime - lastRedisCheckTime < redisDebounceConfig.getRedisCheckInterval()) {
            return false;
        }

        lastRedisCheckTime = currentTime;

        try {
            stringRedisTemplate.hasKey(RedisDebounceConfig.HEALTH_CHECK_KEY);

            // Redis操作成功，增加成功计数
            int successCount = redisConsecutiveSuccessCount.incrementAndGet();
            redisConsecutiveFailureCount.set(0);

            log.info("Redis连接检查成功 ({}/{})", successCount, redisDebounceConfig.getRequiredSuccessCount());

            // 连续成功达到阈值，切换回Redis模式
            if (successCount >= redisDebounceConfig.getRequiredSuccessCount()) {
                redisAvailable = true;
                log.info("Redis连续成功{}次，状态服务切换回Redis模式", successCount);
                return true;
            }

            return false;
        } catch (Exception e) {
            // 重置成功计数
            redisConsecutiveSuccessCount.set(0);
            log.debug("Redis仍不可用，继续使用内存模式: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有Job状态
     */
    public List<JobStatus> getAllJobStatuses() {
        try {
            Set<String> keys = stringRedisTemplate.keys(JobConstants.STATUS_KEY_PREFIX + "*");
            if (keys == null || keys.isEmpty()) {
                return new ArrayList<>();
            }

            List<JobStatus> statuses = new ArrayList<>();
            for (String key : keys) {
                String jobName = key.substring(JobConstants.STATUS_KEY_PREFIX.length());
                JobStatus status = getJobStatus(jobName);
                if (status != null) {
                    statuses.add(status);
                }
            }
            return statuses;
        } catch (Exception e) {
            log.error("获取所有Job状态失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取单个Job状态
     */
    public JobStatus getJobStatus(String jobName) {
        try {
            String statusKey = JobConstants.STATUS_KEY_PREFIX + jobName;
            Map<Object, Object> statusData = stringRedisTemplate.opsForHash().entries(statusKey);
            
            JobStatus status = new JobStatus();
            status.setJobName(jobName);
            
            // 从配置中获取Job基本信息
            try {
                List<JobConfig> configs = jobConfigLoader.loadJobConfigs();
                JobConfig jobConfig = configs.stream()
                        .filter(config -> jobName.equals(config.getJobName()))
                        .findFirst()
                        .orElse(null);
                
                if (jobConfig != null) {
                    status.setType(jobConfig.getType());
                    status.setEnabled(jobConfig.isEnabled());
                    status.setCron(jobConfig.getCron());
                    status.setDescription(jobConfig.getDescription());
                }
            } catch (JobConfigException e) {
                log.warn("获取Job配置失败: {}", jobName, e);
            }
            
            // 从Redis获取运行时状态
            if (!statusData.isEmpty()) {
                status.setLastTriggerTime((String) statusData.get("lastTriggerTime"));
                status.setLastExecutionNode((String) statusData.get("lastExecutionNode"));
                status.setLastExecutionId((String) statusData.get("lastExecutionId"));
                status.setLastStatus((String) statusData.get("lastStatus"));
                status.setLastFinishTime((String) statusData.get("lastFinishTime"));
                status.setLastSuccessTime((String) statusData.get("lastSuccessTime"));
                
                String durationStr = (String) statusData.get("lastDurationMs");
                if (durationStr != null) {
                    status.setLastDurationMs(Long.parseLong(durationStr));
                }
                
                String failCountStr = (String) statusData.get("consecutiveFailCount");
                if (failCountStr != null) {
                    status.setConsecutiveFailCount(Integer.parseInt(failCountStr));
                }
            }
            
            // 检查是否正在运行
            String runningKey = JobConstants.RUNNING_KEY_PREFIX + jobName;
            status.setRunning(stringRedisTemplate.hasKey(runningKey));
            
            return status;
        } catch (Exception e) {
            log.error("获取Job状态失败: {}", jobName, e);
            return null;
        }
    }

    /**
     * 获取所有生效的Job配置
     */
    public List<JobConfig> getAllJobConfigs() {
        try {
            return jobConfigLoader.loadJobConfigs().stream()
                    .filter(JobConfig::isEnabled)
                    .collect(Collectors.toList());
        } catch (JobConfigException e) {
            log.error("获取Job配置失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取当前正在运行的Job
     */
    public List<RunningJobInfo> getRunningJobs() {
        try {
            Set<String> keys = stringRedisTemplate.keys(JobConstants.RUNNING_KEY_PREFIX + "*");
            if (keys == null || keys.isEmpty()) {
                return new ArrayList<>();
            }

            List<RunningJobInfo> runningJobs = new ArrayList<>();
            for (String key : keys) {
                String jobName = key.substring(JobConstants.RUNNING_KEY_PREFIX.length());
                Map<Object, Object> runningData = stringRedisTemplate.opsForHash().entries(key);
                
                if (!runningData.isEmpty()) {
                    RunningJobInfo runningJob = new RunningJobInfo();
                    runningJob.setJobName(jobName);
                    runningJob.setExecutionId((String) runningData.get("executionId"));
                    runningJob.setStartTime((String) runningData.get("startTime"));
                    runningJob.setExecutionNode((String) runningData.get("executionNode"));
                    
                    // 计算运行时长
                    String startTimeStr = (String) runningData.get("startTime");
                    if (startTimeStr != null) {
                        try {
                            LocalDateTime startTime = LocalDateTime.parse(startTimeStr, dateTimeFormatter);
                            LocalDateTime now = LocalDateTime.now();
                            long durationMs = java.time.Duration.between(startTime, now).toMillis();
                            runningJob.setRunningDurationMs(durationMs);
                        } catch (Exception e) {
                            log.warn("计算运行时长失败: {}", jobName, e);
                        }
                    }
                    
                    runningJobs.add(runningJob);
                }
            }
            return runningJobs;
        } catch (Exception e) {
            log.error("获取正在运行的Job失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 记录Job开始执行
     */
    public void recordJobStart(String jobName, String executionId) {
        // 如果Redis不可用，使用内存模式
        if (!isRedisAvailable()) {
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            return;
        }

        try {
            String currentTime = LocalDateTime.now().format(dateTimeFormatter);

            // 更新状态信息
            String statusKey = JobConstants.STATUS_KEY_PREFIX + jobName;
            Map<String, String> statusData = new HashMap<>();
            statusData.put("lastTriggerTime", currentTime);
            statusData.put("lastExecutionNode", currentNode);
            statusData.put("lastExecutionId", executionId);
            stringRedisTemplate.opsForHash().putAll(statusKey, statusData);
            
            // 记录正在运行的Job
            String runningKey = JobConstants.RUNNING_KEY_PREFIX + jobName;
            Map<String, String> runningData = new HashMap<>();
            runningData.put("executionId", executionId);
            runningData.put("startTime", currentTime);
            runningData.put("executionNode", currentNode);
            runningData.put("status", "RUNNING");
            stringRedisTemplate.opsForHash().putAll(runningKey, runningData);
            
            log.info("记录Job开始执行: jobName={}, executionId={}", jobName, executionId);
        } catch (Exception e) {
            log.error("记录Job开始执行失败: jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 记录Job执行完成
     */
    public void recordJobFinish(String jobName, String executionId, JobExecutionResult result) {
        // 如果Redis不可用，使用内存模式
        if (!isRedisAvailable()) {
            inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
            return;
        }

        try {
            String currentTime = LocalDateTime.now().format(dateTimeFormatter);
            String status = result.isSuccess() ? "SUCCESS" : "FAIL";
            
            // 更新状态信息
            String statusKey = JobConstants.STATUS_KEY_PREFIX + jobName;
            Map<String, String> statusData = new HashMap<>();
            statusData.put("lastStatus", status);
            statusData.put("lastFinishTime", currentTime);
            statusData.put("lastDurationMs", String.valueOf(result.getDurationMs()));
            
            if (result.isSuccess()) {
                statusData.put("lastSuccessTime", currentTime);
                statusData.put("consecutiveFailCount", "0");
            } else {
                // 增加连续失败计数
                String failCountStr = (String) stringRedisTemplate.opsForHash().get(statusKey, "consecutiveFailCount");
                int failCount = failCountStr != null ? Integer.parseInt(failCountStr) : 0;
                statusData.put("consecutiveFailCount", String.valueOf(failCount + 1));
            }
            
            stringRedisTemplate.opsForHash().putAll(statusKey, statusData);
            
            // 删除正在运行的记录
            String runningKey = JobConstants.RUNNING_KEY_PREFIX + jobName;
            stringRedisTemplate.delete(runningKey);
            
            // 添加到执行历史
            addToExecutionHistory(jobName, executionId, result);
            
            log.info("记录Job执行完成: jobName={}, executionId={}, status={}", jobName, executionId, status);
        } catch (Exception e) {
            log.error("记录Job执行完成失败: jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 获取Job执行历史
     */
    public List<JobExecutionHistory> getJobHistory(String jobName, int page, int size) {
        try {
            String historyKey = JobConstants.HISTORY_KEY_PREFIX + jobName;
            long start = (long) page * size;
            long end = start + size - 1;
            
            List<String> historyData = stringRedisTemplate.opsForList().range(historyKey, start, end);
            if (historyData == null || historyData.isEmpty()) {
                return new ArrayList<>();
            }
            
            return historyData.stream()
                    .map(this::parseExecutionHistory)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取Job执行历史失败: jobName={}", jobName, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有Job的执行历史
     */
    public List<JobExecutionHistory> getAllJobHistory(int page, int size) {
        try {
            Set<String> keys = stringRedisTemplate.keys(JobConstants.HISTORY_KEY_PREFIX + "*");
            if (keys == null || keys.isEmpty()) {
                return new ArrayList<>();
            }

            List<JobExecutionHistory> allHistory = new ArrayList<>();
            for (String key : keys) {
                String jobName = key.substring(JobConstants.HISTORY_KEY_PREFIX.length());
                List<JobExecutionHistory> jobHistory = getJobHistory(jobName, 0, JobConstants.MAX_HISTORY_SIZE);
                allHistory.addAll(jobHistory);
            }
            
            // 按开始时间倒序排序
            allHistory.sort((h1, h2) -> h2.getStartTime().compareTo(h1.getStartTime()));
            
            // 分页
            int start = page * size;
            int end = Math.min(start + size, allHistory.size());
            
            if (start >= allHistory.size()) {
                return new ArrayList<>();
            }
            
            return allHistory.subList(start, end);
        } catch (Exception e) {
            log.error("获取所有Job执行历史失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 清理过期的执行历史记录
     */
    public void cleanupExpiredHistory() {
        try {
            Set<String> keys = stringRedisTemplate.keys(JobConstants.HISTORY_KEY_PREFIX + "*");
            if (keys == null || keys.isEmpty()) {
                return;
            }

            for (String key : keys) {
                Long size = stringRedisTemplate.opsForList().size(key);
                if (size != null && size > JobConstants.MAX_HISTORY_SIZE) {
                    // 保留最新的MAX_HISTORY_SIZE条记录，删除多余的
                    stringRedisTemplate.opsForList().trim(key, 0, JobConstants.MAX_HISTORY_SIZE - 1);
                    log.info("清理Job执行历史: key={}, 保留{}条记录", key, JobConstants.MAX_HISTORY_SIZE);
                }
            }
        } catch (Exception e) {
            log.error("清理过期执行历史失败", e);
        }
    }

    /**
     * 添加到执行历史
     */
    private void addToExecutionHistory(String jobName, String executionId, JobExecutionResult result) {
        try {
            JobExecutionHistory history = new JobExecutionHistory();
            history.setJobName(jobName);
            history.setExecutionId(executionId);
            history.setStartTime(result.getStartTime());
            history.setFinishTime(result.getEndTime());
            history.setStatus(result.isSuccess() ? "SUCCESS" : "FAIL");
            history.setDurationMs(result.getDurationMs());
            history.setExecutionNode(currentNode);
            history.setRetryCount(result.getRetryCount());
            history.setErrorMessage(result.getErrorMessage());
            
            String historyJson = objectMapper.writeValueAsString(history);
            String historyKey = JobConstants.HISTORY_KEY_PREFIX + jobName;
            
            // 添加到列表头部（最新的记录在前面）
            stringRedisTemplate.opsForList().leftPush(historyKey, historyJson);
            
            // 保持列表大小不超过最大限制
            stringRedisTemplate.opsForList().trim(historyKey, 0, JobConstants.MAX_HISTORY_SIZE - 1);
        } catch (Exception e) {
            log.error("添加执行历史失败: jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 解析执行历史JSON
     */
    private JobExecutionHistory parseExecutionHistory(String historyJson) {
        try {
            return objectMapper.readValue(historyJson, JobExecutionHistory.class);
        } catch (JsonProcessingException e) {
            log.error("解析执行历史JSON失败: {}", historyJson, e);
            return null;
        }
    }

    /**
     * 获取当前节点ID
     */
    private String getCurrentNodeId() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            log.warn("获取主机名失败，使用默认值", e);
            return "unknown-node";
        }
    }

    /**
     * 手动检查Redis恢复状态（带防抖动机制）
     *
     * @return Redis是否可用
     */
    public boolean checkAndRecoverRedis() {
        if (stringRedisTemplate == null) {
            return false;
        }

        try {
            // 强制检查Redis连接
            stringRedisTemplate.hasKey(RedisDebounceConfig.HEALTH_CHECK_KEY);

            boolean wasUnavailable = !redisAvailable;

            if (wasUnavailable) {
                // Redis从不可用恢复，需要连续成功验证
                int successCount = redisConsecutiveSuccessCount.incrementAndGet();
                redisConsecutiveFailureCount.set(0);

                log.info("手动检查Redis连接成功 ({}/{})", successCount, redisDebounceConfig.getRequiredSuccessCount());

                if (successCount >= redisDebounceConfig.getRequiredSuccessCount()) {
                    redisAvailable = true;
                    lastRedisCheckTime = System.currentTimeMillis();
                    log.info("手动检查Redis连续成功{}次，状态服务已切换回Redis模式", successCount);
                    return true;
                } else {
                    log.info("Redis连接成功，但需要连续成功{}次才能切换回Redis模式", redisDebounceConfig.getRequiredSuccessCount());
                    return false;
                }
            } else {
                // Redis已经可用，重置失败计数
                redisConsecutiveFailureCount.set(0);
                lastRedisCheckTime = System.currentTimeMillis();
                log.debug("手动检查Redis连接稳定");
                return true;
            }

        } catch (Exception e) {
            log.debug("手动检查Redis仍不可用: {}", e.getMessage());

            // 重置成功计数，增加失败计数
            redisConsecutiveSuccessCount.set(0);

            if (redisAvailable) {
                int failureCount = redisConsecutiveFailureCount.incrementAndGet();
                log.warn("手动检查Redis失败 ({}/{}): {}", failureCount, redisDebounceConfig.getRequiredFailureCount(), e.getMessage());

                if (failureCount >= redisDebounceConfig.getRequiredFailureCount()) {
                    redisAvailable = false;
                    log.warn("Redis连续失败{}次，状态服务切换到内存模式", failureCount);
                }
            }

            return false;
        }
    }

    /**
     * 获取当前存储模式
     *
     * @return 存储模式描述
     */
    public String getStorageMode() {
        if (stringRedisTemplate == null) {
            return "MEMORY_MODE (Redis未配置)";
        } else if (!redisAvailable) {
            return "MEMORY_MODE (Redis不可用)";
        } else {
            return "REDIS_MODE (Redis可用)";
        }
    }
}