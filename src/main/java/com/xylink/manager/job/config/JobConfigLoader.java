package com.xylink.manager.job.config;

import com.xylink.manager.job.exception.JobConfigException;
import com.xylink.manager.job.model.JobConfig;
import com.xylink.manager.job.model.JobSystemConfig;
import com.xylink.manager.job.model.JobType;
import com.xylink.manager.job.model.JobsConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Job配置加载器
 * 支持从classpath和外部文件加载YAML配置
 */
@Component
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class JobConfigLoader {
    
    private static final Logger logger = LoggerFactory.getLogger(JobConfigLoader.class);
    
    private static final String DEFAULT_CONFIG_FILE = "jobs.yaml";
    private static final String EXTERNAL_CONFIG_PATH_PROPERTY = "job.config.path";
    
    private final Validator validator;
    private final Yaml yaml;
    
    public JobConfigLoader(Validator validator) {
        this.validator = validator;
        LoaderOptions loaderOptions = new LoaderOptions();
        Constructor constructor = new Constructor(JobsConfig.class, loaderOptions);
        this.yaml = new Yaml(constructor);
    }
    
    /**
     * 加载完整的Jobs配置（包含系统配置和任务配置）
     * 优先从系统属性指定的外部文件加载，如果不存在则从classpath加载
     * 
     * @return Jobs配置对象
     * @throws JobConfigException 配置加载或验证失败时抛出
     */
    public JobsConfig loadJobsConfig() throws JobConfigException {
        logger.info("开始加载Jobs配置文件");

        Resource configResource = getConfigResource();

        // 如果配置文件不存在，返回默认的空配置
        if (configResource == null) {
            logger.info("配置文件不存在，使用默认空配置，Job模块将以禁用模式运行");
            JobsConfig defaultConfig = new JobsConfig();
            defaultConfig.setSystem(new JobSystemConfig());
            defaultConfig.setJobs(new ArrayList<>());
            return defaultConfig;
        }

        logger.info("使用配置文件: {}", configResource.getDescription());

        try (InputStream inputStream = configResource.getInputStream()) {
            JobsConfig jobsConfig = yaml.load(inputStream);
            
            if (jobsConfig == null) {
                throw new JobConfigException("配置文件为空");
            }
            
            // 如果没有system配置，使用默认值
            if (jobsConfig.getSystem() == null) {
                jobsConfig.setSystem(new JobSystemConfig());
            }
            
            // 如果没有jobs配置，设置为空列表
            if (jobsConfig.getJobs() == null) {
                jobsConfig.setJobs(new ArrayList<>());
            }
            
            // 验证配置
            validateJobsConfig(jobsConfig);
            
            logger.info("成功加载Jobs配置，系统配置: {}, 任务数量: {}", 
                    jobsConfig.getSystem() != null, jobsConfig.getJobs().size());
            return jobsConfig;
            
        } catch (IOException e) {
            throw new JobConfigException("读取配置文件失败: " + e.getMessage(), e);
        } catch (Exception e) {
            if (e instanceof JobConfigException) {
                throw e;
            }
            throw new JobConfigException("解析配置文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载Job配置列表（保持向后兼容）
     * 
     * @return Job配置列表
     * @throws JobConfigException 配置加载或验证失败时抛出
     */
    public List<JobConfig> loadJobConfigs() throws JobConfigException {
        JobsConfig jobsConfig = loadJobsConfig();
        
        List<JobConfig> validConfigs = jobsConfig.getJobs().stream()
                .filter(this::isValidJobConfig)
                .collect(Collectors.toList());
        
        logger.info("成功加载{}个Job配置", validConfigs.size());
        return validConfigs;
    }
    
    /**
     * 获取配置资源
     * 优先从系统属性指定的外部文件加载，如果不存在则从classpath加载
     */
    private Resource getConfigResource() throws JobConfigException {
        String externalConfigPath = System.getProperty(EXTERNAL_CONFIG_PATH_PROPERTY);
        
        if (StringUtils.hasText(externalConfigPath)) {
            Path configPath = Paths.get(externalConfigPath);
            if (Files.exists(configPath)) {
                logger.info("使用外部配置文件: {}", externalConfigPath);
                return new FileSystemResource(configPath);
            } else {
                logger.warn("指定的外部配置文件不存在: {}, 将使用classpath中的配置", externalConfigPath);
            }
        }
        
        // 从classpath加载
        ClassPathResource classpathResource = new ClassPathResource(DEFAULT_CONFIG_FILE);
        if (!classpathResource.exists()) {
            logger.warn("配置文件不存在: {} (classpath) 或 {} (外部文件)，Job模块将以禁用模式启动",
                    DEFAULT_CONFIG_FILE, externalConfigPath);
            return null; // 返回null表示无配置文件
        }

        return classpathResource;
    }
    
    /**
     * 验证Jobs配置
     */
    private void validateJobsConfig(JobsConfig jobsConfig) throws JobConfigException {
        Set<ConstraintViolation<JobsConfig>> violations = validator.validate(jobsConfig);
        if (!violations.isEmpty()) {
            String errorMessage = violations.stream()
                    .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                    .collect(Collectors.joining("; "));
            throw new JobConfigException("配置验证失败: " + errorMessage);
        }
        
        // 验证每个Job配置
        for (JobConfig jobConfig : jobsConfig.getJobs()) {
            validateJobConfig(jobConfig);
        }
        
        // 检查Job名称唯一性
        List<String> jobNames = jobsConfig.getJobs().stream()
                .map(JobConfig::getJobName)
                .collect(Collectors.toList());
        
        Set<String> uniqueJobNames = jobNames.stream().collect(Collectors.toSet());
        if (jobNames.size() != uniqueJobNames.size()) {
            throw new JobConfigException("存在重复的Job名称");
        }
    }
    
    /**
     * 验证单个Job配置
     */
    public void validateJobConfig(JobConfig config) throws JobConfigException {
        if (config == null) {
            throw new JobConfigException("Job配置不能为空");
        }
        
        // Bean Validation验证
        Set<ConstraintViolation<JobConfig>> violations = validator.validate(config);
        if (!violations.isEmpty()) {
            String errorMessage = violations.stream()
                    .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                    .collect(Collectors.joining("; "));
            throw new JobConfigException("Job配置验证失败 [" + config.getJobName() + "]: " + errorMessage);
        }
        
        // 验证Cron表达式
        validateCronExpression(config.getCron());
        
        // 验证Job类型特定配置
        validateJobTypeSpecificConfig(config);
    }
    
    /**
     * 验证Cron表达式
     */
    public void validateCronExpression(String cron) throws JobConfigException {
        if (!StringUtils.hasText(cron)) {
            throw new JobConfigException("Cron表达式不能为空");
        }
        
        try {
            CronExpression.parse(cron);
        } catch (IllegalArgumentException e) {
            throw new JobConfigException("无效的Cron表达式: " + cron + ", 错误: " + e.getMessage());
        }
    }
    
    /**
     * 验证Job类型特定配置
     */
    private void validateJobTypeSpecificConfig(JobConfig config) throws JobConfigException {
        JobType type = config.getType();
        
        if (type == JobType.API) {
            if (config.getApiConfig() == null) {
                throw new JobConfigException("API类型Job必须配置apiConfig [" + config.getJobName() + "]");
            }
            if (config.getScriptConfig() != null) {
                throw new JobConfigException("API类型Job不应配置scriptConfig [" + config.getJobName() + "]");
            }
        } else if (type == JobType.SCRIPT) {
            if (config.getScriptConfig() == null) {
                throw new JobConfigException("SCRIPT类型Job必须配置scriptConfig [" + config.getJobName() + "]");
            }
            if (config.getApiConfig() != null) {
                throw new JobConfigException("SCRIPT类型Job不应配置apiConfig [" + config.getJobName() + "]");
            }
        } else {
            throw new JobConfigException("不支持的Job类型: " + type + " [" + config.getJobName() + "]");
        }
    }
    
    /**
     * 检查Job配置是否有效（用于过滤）
     */
    private boolean isValidJobConfig(JobConfig config) {
        try {
            validateJobConfig(config);
            return true;
        } catch (JobConfigException e) {
            logger.warn("跳过无效的Job配置 [{}]: {}", config.getJobName(), e.getMessage());
            return false;
        }
    }
}