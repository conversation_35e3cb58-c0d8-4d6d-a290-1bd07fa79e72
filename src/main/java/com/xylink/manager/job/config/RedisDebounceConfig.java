package com.xylink.manager.job.config;

import com.xylink.manager.job.model.JobsConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Redis防抖动配置
 *
 * 用于配置Redis连接状态检查的防抖动参数，防止Redis不稳定时频繁切换模式
 * 从jobs.yaml文件的system.redis.debounce部分读取配置
 */
@Component
public class RedisDebounceConfig {

    @Autowired
    private JobConfigLoader jobConfigLoader;

    /**
     * 默认的切换到分布式模式需要的连续成功次数
     */
    private static final int DEFAULT_REQUIRED_SUCCESS_COUNT = 3;

    /**
     * 默认的切换到内存模式需要的连续失败次数
     */
    private static final int DEFAULT_REQUIRED_FAILURE_COUNT = 2;

    /**
     * 默认的Redis可用性检查间隔（秒）
     */
    private static final int DEFAULT_CHECK_INTERVAL_SECONDS = 30;
    
    /**
     * Redis健康检查的键名
     *
     * 用于检查Redis连接状态的测试键名
     */
    public static final String HEALTH_CHECK_KEY = "job:health:check";

    // Getter方法
    public int getRequiredSuccessCount() {
        try {
            JobsConfig config = jobConfigLoader.loadJobsConfig();
            if (config != null && config.getSystem() != null &&
                config.getSystem().getRedis() != null &&
                config.getSystem().getRedis().getDebounce() != null) {
                Integer value = config.getSystem().getRedis().getDebounce().getRequiredSuccessCount();
                if (value != null && value > 0) {
                    return value;
                }
            }
        } catch (Exception e) {
            // 配置读取失败时使用默认值
        }
        return DEFAULT_REQUIRED_SUCCESS_COUNT;
    }

    public int getRequiredFailureCount() {
        try {
            JobsConfig config = jobConfigLoader.loadJobsConfig();
            if (config != null && config.getSystem() != null &&
                config.getSystem().getRedis() != null &&
                config.getSystem().getRedis().getDebounce() != null) {
                Integer value = config.getSystem().getRedis().getDebounce().getRequiredFailureCount();
                if (value != null && value > 0) {
                    return value;
                }
            }
        } catch (Exception e) {
            // 配置读取失败时使用默认值
        }
        return DEFAULT_REQUIRED_FAILURE_COUNT;
    }

    public long getRedisCheckInterval() {
        try {
            JobsConfig config = jobConfigLoader.loadJobsConfig();
            if (config != null && config.getSystem() != null &&
                config.getSystem().getRedis() != null &&
                config.getSystem().getRedis().getDebounce() != null) {
                Integer value = config.getSystem().getRedis().getDebounce().getCheckIntervalSeconds();
                if (value != null && value > 0) {
                    return value * 1000L; // 转换为毫秒
                }
            }
        } catch (Exception e) {
            // 配置读取失败时使用默认值
        }
        return DEFAULT_CHECK_INTERVAL_SECONDS * 1000L;
    }

    /**
     * 获取防抖动配置说明
     *
     * @return 防抖动机制的详细说明
     */
    public String getDebounceDescription() {
        return String.format(
            "防抖动机制：连续%d次失败切换到内存模式，连续%d次成功切换到分布式模式，检查间隔%d秒",
            getRequiredFailureCount(),
            getRequiredSuccessCount(),
            getRedisCheckInterval() / 1000
        );
    }
    
    /**
     * 获取防抖动配置信息
     *
     * @return 包含所有防抖动参数的配置信息
     */
    public java.util.Map<String, Object> getDebounceConfig() {
        java.util.Map<String, Object> config = new java.util.HashMap<>();
        config.put("requiredSuccessCount", getRequiredSuccessCount());
        config.put("requiredFailureCount", getRequiredFailureCount());
        config.put("checkIntervalMs", getRedisCheckInterval());
        config.put("checkIntervalSeconds", getRedisCheckInterval() / 1000);
        config.put("healthCheckKey", HEALTH_CHECK_KEY);
        config.put("description", getDebounceDescription());
        return config;
    }
    
    /**
     * 验证防抖动参数的合理性
     *
     * @throws IllegalArgumentException 如果参数不合理
     */
    public void validateConfig() {
        int successCount = getRequiredSuccessCount();
        int failureCount = getRequiredFailureCount();
        long checkInterval = getRedisCheckInterval();

        if (successCount <= 0) {
            throw new IllegalArgumentException("requiredSuccessCount必须大于0");
        }

        if (failureCount <= 0) {
            throw new IllegalArgumentException("requiredFailureCount必须大于0");
        }

        if (checkInterval <= 0) {
            throw new IllegalArgumentException("redisCheckInterval必须大于0");
        }

        if (successCount < failureCount) {
            // 这不是错误，但可能需要注意
            System.out.println("注意：成功次数要求(" + successCount +
                             ")小于失败次数要求(" + failureCount +
                             ")，这可能导致更频繁的模式切换");
        }
    }

    /**
     * 打印防抖动配置信息
     */
    public void printConfig() {
        System.out.println("=== Redis防抖动配置 ===");
        System.out.println("切换到分布式模式需要连续成功次数: " + getRequiredSuccessCount());
        System.out.println("切换到内存模式需要连续失败次数: " + getRequiredFailureCount());
        System.out.println("检查间隔: " + (getRedisCheckInterval() / 1000) + "秒");
        System.out.println("健康检查键: " + HEALTH_CHECK_KEY);
        System.out.println("说明: " + getDebounceDescription());
        System.out.println("========================");
    }

}
