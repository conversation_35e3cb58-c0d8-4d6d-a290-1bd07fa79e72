# Job配置统一化迁移指南

## 概述

为了提高配置管理的一致性和可维护性，我们将所有Job相关配置统一迁移到`jobs.yaml`文件中。

## 迁移前后对比

### 迁移前

配置分散在两个文件中：

**application.properties**
```properties
# Job优雅停机超时时间（秒）
job.shutdown.timeout=60
```

**jobs.yaml**
```yaml
jobs:
  - jobName: sync-user-data
    type: API
    cron: "0 0 1 * * ?"
    # ... 其他配置
```

### 迁移后

所有配置统一在`jobs.yaml`中：

```yaml
# Job调度系统配置
system:
  shutdownTimeoutSeconds: 60
  executor:
    corePoolSize: 5
    maxPoolSize: 20
    queueCapacity: 100
  scheduler:
    poolSize: 10
    awaitTerminationSeconds: 30
  lock:
    defaultTimeoutSeconds: 30
  metrics:
    enabled: true
    pushIntervalSeconds: 60

# Job任务定义
jobs:
  - jobName: sync-user-data
    type: API
    cron: "0 0 1 * * ?"
    # ... 其他配置
```

## 配置项说明

### 系统配置 (system)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `shutdownTimeoutSeconds` | int | 60 | 优雅停机超时时间（秒） |

#### 执行器配置 (executor)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `corePoolSize` | int | 5 | 核心线程数 |
| `maxPoolSize` | int | 20 | 最大线程数 |
| `queueCapacity` | int | 100 | 队列容量 |

#### 调度器配置 (scheduler)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `poolSize` | int | 10 | 调度器线程池大小 |
| `awaitTerminationSeconds` | int | 30 | 等待终止时间（秒） |

#### 分布式锁配置 (lock)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `defaultTimeoutSeconds` | int | 30 | 默认锁超时时间（秒） |

#### 监控配置 (metrics)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | boolean | true | 是否启用监控 |
| `pushIntervalSeconds` | int | 60 | 监控数据推送间隔（秒） |

## 迁移步骤

### 1. 更新jobs.yaml文件

在现有的`jobs.yaml`文件顶部添加`system`配置节：

```yaml
# 添加系统配置
system:
  shutdownTimeoutSeconds: 60  # 从application.properties迁移
  # 其他配置使用默认值或根据需要调整

# 保持现有的jobs配置不变
jobs:
  # ... 现有配置
```

### 2. 从application.properties中移除Job配置

删除以下配置项：
```properties
# 删除这些配置
job.shutdown.timeout=60
```

### 3. 验证配置

启动应用并检查日志，确认配置加载正常：

```
INFO - 成功加载Jobs配置，系统配置: true, 任务数量: 3
INFO - 从配置中加载停机超时时间: 60秒
```

## 向后兼容性

- 如果`jobs.yaml`中没有`system`配置节，系统会使用默认值
- 现有的Job配置格式保持不变
- 提供了`JobConfigService`统一配置访问接口

## 配置验证

系统会自动验证配置的有效性：

- 数值类型配置必须大于0
- 必需的配置项不能为空
- 配置格式必须符合YAML规范

## 环境特定配置

可以通过系统属性指定不同环境的配置文件：

```bash
# 开发环境
-Djob.config.path=/path/to/dev-jobs.yaml

# 生产环境
-Djob.config.path=/path/to/prod-jobs.yaml
```

## 配置热更新

配置更新后，可以通过以下方式重新加载：

1. **重启应用**（推荐）
2. **调用重载接口**（如果实现了热更新功能）

## 故障排查

### 常见问题

1. **配置文件格式错误**
   ```
   ERROR - 解析配置文件失败: mapping values are not allowed here
   ```
   解决：检查YAML格式，确保缩进正确

2. **配置验证失败**
   ```
   ERROR - 配置验证失败: shutdownTimeoutSeconds: 优雅停机超时时间必须大于0
   ```
   解决：检查配置值是否符合验证规则

3. **配置文件不存在**
   ```
   ERROR - 配置文件不存在: jobs.yaml (classpath)
   ```
   解决：确保配置文件存在于正确的位置

### 调试技巧

1. **启用调试日志**
   ```properties
   logging.level.com.xylink.manager.job.config=DEBUG
   ```

2. **检查配置加载**
   ```java
   @Autowired
   private JobConfigService jobConfigService;
   
   // 获取系统配置
   JobSystemConfig systemConfig = jobConfigService.getSystemConfig();
   log.info("当前系统配置: {}", systemConfig);
   ```

## 最佳实践

1. **配置分层**：将通用配置放在基础配置文件中，环境特定配置放在专门的文件中
2. **配置验证**：在部署前验证配置文件的正确性
3. **配置备份**：定期备份配置文件，特别是生产环境
4. **配置文档**：为每个配置项添加清晰的注释说明
5. **版本控制**：将配置文件纳入版本控制系统

## 相关文档

- [Job调度系统设计文档](../scheduler/README.md)
- [Job配置参考](../model/README.md)
- [优雅停机机制](../shutdown/README.md)