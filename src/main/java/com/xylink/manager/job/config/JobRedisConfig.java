package com.xylink.manager.job.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Job模块Redis配置类 - 严格条件化版本
 * 只有在明确启用Redis功能时才会加载此配置
 * 统一使用Redisson作为Redis客户端，提供更好的性能和分布式功能
 */
@Configuration
@ConditionalOnProperty(name = "redis.enabled", havingValue = "true", matchIfMissing = false)
@ConditionalOnClass({RedissonClient.class, RedisTemplate.class})
public class JobRedisConfig {

    @Value("${spring.redis.host:127.0.0.1}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.database:0}")
    private int redisDatabase;

    @Value("${spring.redis.timeout:2000}")
    private int redisTimeout;

    /**
     * 创建RedissonClient用于分布式锁和高级Redis功能
     * 优化配置以提供更好的性能和稳定性
     */
    @Bean
    @ConditionalOnMissingBean(RedissonClient.class)
    public RedissonClient redissonClient() {
        Config config = new Config();

        String redisUrl = "redis://" + redisHost + ":" + redisPort;
        config.useSingleServer()
                .setAddress(redisUrl)
                .setDatabase(redisDatabase)
                .setTimeout(redisTimeout)
                // 连接池优化配置
                .setConnectionMinimumIdleSize(2)  // 最小空闲连接数
                .setConnectionPoolSize(20)        // 最大连接池大小
                .setIdleConnectionTimeout(10000)  // 空闲连接超时时间(ms)
                .setConnectTimeout(10000)         // 连接超时时间(ms)
                .setRetryAttempts(3)              // 重试次数
                .setRetryInterval(1500)           // 重试间隔(ms)
                // 性能优化
                .setKeepAlive(true)               // 启用TCP keepalive
                .setTcpNoDelay(true);             // 禁用Nagle算法，减少延迟

        // 如果有密码则设置密码
        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            config.useSingleServer().setPassword(redisPassword);
        }

        // 设置编解码器为JSON格式，提高可读性
        // config.setCodec(new org.redisson.codec.JsonJacksonCodec());

        return Redisson.create(config);
    }

    /**
     * 创建StringRedisTemplate用于Job状态存储
     * 如果项目中已存在StringRedisTemplate，则复用现有的
     */
    @Bean
    @ConditionalOnMissingBean(StringRedisTemplate.class)
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory connectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(connectionFactory);
        return template;
    }

    /**
     * 创建RedisConnectionFactory使用Redisson实现
     * 提供更好的性能和分布式功能支持
     */
    @Bean
    @ConditionalOnMissingBean(RedisConnectionFactory.class)
    public RedisConnectionFactory redisConnectionFactory(RedissonClient redissonClient) {
        return new RedissonConnectionFactory(redissonClient);
    }

    /**
     * 创建RedisTemplate用于复杂对象存储
     * 使用Redisson作为底层连接工厂
     */
    @Bean
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 设置序列化器 - 优化JSON序列化配置
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();

        // 设置key和value的序列化器
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);

        // 启用事务支持
        template.setEnableTransactionSupport(true);
        template.afterPropertiesSet();
        return template;
    }
}