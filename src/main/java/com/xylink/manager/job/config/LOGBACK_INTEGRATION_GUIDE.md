# Job调度系统日志配置集成指南

## 概述

本指南说明如何将Job调度系统的日志配置集成到现有的logback配置中。Job调度系统提供了独立的日志配置，包括执行日志、安全审计日志和管理操作日志。

## 日志分类

Job调度系统产生以下几类日志：

1. **Job执行日志** - 记录任务的执行过程和结果
2. **安全审计日志** - 记录安全相关事件和违规行为
3. **管理操作日志** - 记录管理员的操作行为
4. **系统日志** - 记录系统启动、配置变更等事件

## 集成方式

### 方式一：包含独立配置文件（推荐）

在现有的`logback-spring.xml`中包含Job日志配置：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <!-- 现有的配置... -->
    
    <!-- 包含Job安全审计日志配置 -->
    <include resource="logback-job-security.xml"/>
    
    <!-- 现有的其他配置... -->
</configuration>
```

### 方式二：直接集成配置

如果不想使用独立配置文件，可以直接将以下配置添加到现有的logback配置中：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <!-- 现有的配置... -->
    
    <!-- Job执行日志配置 -->
    <appender name="JOB_EXECUTION_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/job-execution.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/job-execution.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <pattern>yyyy-MM-dd HH:mm:ss.SSS</pattern>
                </timestamp>
                <logLevel/>
                <loggerName/>
                <mdc>
                    <includeKeyName>executionId</includeKeyName>
                    <includeKeyName>jobName</includeKeyName>
                </mdc>
                <message/>
                <stackTrace/>
            </providers>
        </encoder>
    </appender>

    <!-- 安全审计日志配置 -->
    <appender name="JOB_SECURITY_AUDIT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/job-security-audit.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/job-security-audit.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 管理操作审计日志配置 -->
    <appender name="JOB_MANAGEMENT_AUDIT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/job-management-audit.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/job-management-audit.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- Job执行日志记录器 -->
    <logger name="com.xylink.manager.job" level="INFO" additivity="false">
        <appender-ref ref="JOB_EXECUTION_FILE"/>
    </logger>

    <!-- 安全审计日志记录器 -->
    <logger name="com.xylink.manager.job.security.audit" level="INFO" additivity="false">
        <appender-ref ref="JOB_SECURITY_AUDIT_FILE"/>
        <!-- 高风险安全事件同时输出到控制台 -->
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- 管理操作审计日志记录器 -->
    <logger name="com.xylink.manager.job.management.audit" level="INFO" additivity="false">
        <appender-ref ref="JOB_MANAGEMENT_AUDIT_FILE"/>
    </logger>

    <!-- Job安全相关的其他日志 -->
    <logger name="com.xylink.manager.job.security" level="DEBUG" additivity="true">
        <appender-ref ref="JOB_SECURITY_AUDIT_FILE"/>
    </logger>

    <!-- 现有的其他配置... -->
</configuration>
```

## 日志文件说明

### 1. job-execution.log
**用途：** 记录Job的执行过程和结果

**格式：** JSON格式，便于日志分析工具处理

**包含信息：**
- 时间戳
- 日志级别
- 记录器名称
- MDC上下文（executionId, jobName）
- 消息内容
- 异常堆栈（如果有）

**示例：**
```json
{
  "timestamp": "2024-01-15 10:30:00.123",
  "level": "INFO",
  "logger": "com.xylink.manager.job.executor.JobExecutor",
  "mdc": {
    "executionId": "abc123def456",
    "jobName": "sync-user-data"
  },
  "message": "Job执行成功: jobName=sync-user-data, executionId=abc123def456, duration=5000ms"
}
```

### 2. job-security-audit.log
**用途：** 记录安全相关事件和违规行为

**格式：** 文本格式，便于人工阅读

**包含信息：**
- 脚本安全验证事件
- 命令注入检测
- 路径遍历攻击检测
- 权限违规事件
- 异常行为检测

**示例：**
```
2024-01-15 10:30:00.123 [job-executor-1] ERROR com.xylink.manager.job.security.audit - 检测到潜在命令注入攻击: {"timestamp":"2024-01-15 10:30:00.123","eventType":"COMMAND_INJECTION_DETECTION","jobName":"test-job","executionId":"exec-123","inputType":"scriptArgument","suspiciousInput":"arg; rm -rf /","detectionReason":"脚本参数包含命令注入字符"}
```

### 3. job-management-audit.log
**用途：** 记录管理员的操作行为

**格式：** 文本格式，便于审计

**包含信息：**
- Job启用/禁用操作
- 配置重载操作
- 手动触发操作
- 强制释放锁操作

**示例：**
```
2024-01-15 10:30:00.123 [http-nio-8080-exec-1] INFO com.xylink.manager.job.management.audit - Job管理操作成功: {"timestamp":"2024-01-15 10:30:00.123","operation":"TRIGGER_JOB","jobName":"sync-user-data","operator":"admin","operationDetails":{"clientIp":"*************","triggerType":"MANUAL"},"success":true,"result":"Job手动触发成功"}
```

## 配置参数说明

### 文件路径配置
```xml
<file>${LOG_PATH:-logs}/job-execution.log</file>
```
- `${LOG_PATH}`：日志目录环境变量
- `logs`：默认日志目录（如果环境变量未设置）

### 滚动策略配置
```xml
<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
    <fileNamePattern>${LOG_PATH:-logs}/job-execution.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
    <maxHistory>30</maxHistory>
</rollingPolicy>
```
- 按天滚动日志文件
- 保留30天的历史日志
- 自动压缩历史日志文件

### 大小和时间滚动策略
```xml
<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
    <fileNamePattern>${LOG_PATH:-logs}/job-security-audit.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
    <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>100MB</maxFileSize>
    </timeBasedFileNamingAndTriggeringPolicy>
    <maxHistory>30</maxHistory>
    <totalSizeCap>10GB</totalSizeCap>
</rollingPolicy>
```
- 按天滚动，单个文件最大100MB
- 保留30天历史，总大小不超过10GB
- 超过限制时自动删除最旧的文件

## MDC上下文配置

Job调度系统使用MDC（Mapped Diagnostic Context）来传递上下文信息：

### 可用的MDC键
- `executionId`：任务执行ID，用于跟踪单次执行
- `jobName`：任务名称
- `sessionId`：会话ID（管理操作时）
- `clientIp`：客户端IP（管理操作时）

### 在日志格式中使用MDC
```xml
<!-- 文本格式 -->
<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{executionId}] %logger{50} - %msg%n</pattern>

<!-- JSON格式 -->
<encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
    <providers>
        <mdc>
            <includeKeyName>executionId</includeKeyName>
            <includeKeyName>jobName</includeKeyName>
        </mdc>
    </providers>
</encoder>
```

## 日志级别配置

### 推荐的日志级别设置

```xml
<!-- Job执行日志：INFO级别，记录重要的执行信息 -->
<logger name="com.xylink.manager.job" level="INFO"/>

<!-- 安全审计日志：INFO级别，记录所有安全事件 -->
<logger name="com.xylink.manager.job.security.audit" level="INFO"/>

<!-- 管理操作日志：INFO级别，记录所有管理操作 -->
<logger name="com.xylink.manager.job.management.audit" level="INFO"/>

<!-- 调试时可以启用DEBUG级别 -->
<logger name="com.xylink.manager.job.security" level="DEBUG"/>
```

### 动态调整日志级别

可以通过JMX或Spring Boot Actuator动态调整日志级别：

```bash
# 通过Actuator调整日志级别
curl -X POST "http://localhost:8080/actuator/loggers/com.xylink.manager.job" \
     -H "Content-Type: application/json" \
     -d '{"configuredLevel": "DEBUG"}'
```

## 性能考虑

### 异步日志配置

对于高并发场景，建议使用异步日志以提高性能：

```xml
<!-- 异步appender -->
<appender name="ASYNC_JOB_EXECUTION" class="ch.qos.logback.classic.AsyncAppender">
    <appender-ref ref="JOB_EXECUTION_FILE"/>
    <queueSize>1000</queueSize>
    <discardingThreshold>0</discardingThreshold>
    <includeCallerData>false</includeCallerData>
</appender>

<!-- 使用异步appender -->
<logger name="com.xylink.manager.job" level="INFO" additivity="false">
    <appender-ref ref="ASYNC_JOB_EXECUTION"/>
</logger>
```

### 日志缓冲配置

```xml
<appender name="JOB_EXECUTION_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 启用缓冲以提高性能 -->
    <immediateFlush>false</immediateFlush>
    <!-- 其他配置... -->
</appender>
```

## 监控和告警

### 日志监控指标

建议监控以下日志指标：

1. **错误日志数量**：监控ERROR级别日志的数量
2. **安全事件数量**：监控安全审计日志中的高风险事件
3. **日志文件大小**：监控日志文件增长速度
4. **磁盘空间使用**：监控日志目录的磁盘使用情况

### 告警配置示例

```yaml
# Prometheus告警规则示例
groups:
  - name: job-scheduler-logs
    rules:
      - alert: JobExecutionErrors
        expr: increase(logback_events_total{logger="com.xylink.manager.job",level="error"}[5m]) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Job执行错误过多"
          description: "在过去5分钟内检测到{{ $value }}个Job执行错误"

      - alert: SecurityViolations
        expr: increase(logback_events_total{logger="com.xylink.manager.job.security.audit",level="error"}[1m]) > 0
        for: 0m
        labels:
          severity: critical
        annotations:
          summary: "检测到安全违规事件"
          description: "检测到Job调度系统安全违规事件"
```

## 日志分析

### 使用ELK Stack分析日志

1. **Filebeat配置**：
```yaml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /logs/job-execution.log
  fields:
    log_type: job_execution
  json.keys_under_root: true

- type: log
  enabled: true
  paths:
    - /logs/job-security-audit.log
  fields:
    log_type: security_audit
```

2. **Logstash过滤器**：
```ruby
filter {
  if [fields][log_type] == "job_execution" {
    # JSON日志已经解析，无需额外处理
  } else if [fields][log_type] == "security_audit" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:level} %{DATA:logger} - %{GREEDYDATA:audit_message}" }
    }
  }
}
```

3. **Kibana仪表板**：
- Job执行成功率趋势
- 安全事件分布图
- 管理操作审计表

### 常用查询示例

```bash
# 查询特定Job的执行日志
grep "jobName=sync-user-data" /logs/job-execution.log

# 查询安全违规事件
grep "COMMAND_INJECTION_DETECTION\|PATH_TRAVERSAL_DETECTION" /logs/job-security-audit.log

# 查询管理操作
grep "TRIGGER_JOB\|ENABLE_JOB\|DISABLE_JOB" /logs/job-management-audit.log

# 统计Job执行成功率
grep "Job执行成功" /logs/job-execution.log | wc -l
grep "Job执行失败" /logs/job-execution.log | wc -l
```

## 故障排查

### 常见问题

1. **日志文件未生成**
   - 检查日志目录权限
   - 检查磁盘空间
   - 检查logback配置语法

2. **日志级别不正确**
   - 检查logger配置
   - 检查additivity设置
   - 检查继承关系

3. **性能问题**
   - 考虑使用异步日志
   - 调整缓冲设置
   - 检查磁盘I/O性能

### 调试配置

启用logback调试模式：

```xml
<configuration debug="true">
    <!-- 配置内容... -->
</configuration>
```

或者通过系统属性：
```bash
-Dlogback.debug=true
```

## 最佳实践

1. **日志分离**：将不同类型的日志分别存储到不同文件
2. **合理滚动**：根据日志量设置合适的滚动策略
3. **性能优化**：高并发场景使用异步日志
4. **监控告警**：设置关键日志的监控和告警
5. **定期清理**：设置合理的日志保留期限
6. **安全保护**：确保日志文件的访问权限
7. **结构化日志**：使用JSON格式便于分析
8. **上下文信息**：充分利用MDC传递上下文

## 相关文档

- [Job配置参考文档](JOB_CONFIG_REFERENCE.md)
- [安全配置指南](../security/README.md)
- [监控指标说明](../metrics/README.md)