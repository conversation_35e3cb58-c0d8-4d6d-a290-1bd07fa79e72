# Job配置参考文档

## 概述

本文档详细说明了Job调度系统的配置文件格式和各个配置项的含义。配置文件使用YAML格式，分为系统配置（system）和任务配置（jobs）两个主要部分。

## 配置文件结构

```yaml
# 系统配置
system:
  # 系统级配置项...

# 任务配置
jobs:
  - jobName: "任务名称"
    # 任务配置项...
```

## 系统配置（system）

### 基本配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `shutdownTimeoutSeconds` | int | 60 | 优雅停机超时时间（秒） |

### 执行器配置（executor）

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `corePoolSize` | int | 5 | 核心线程数 |
| `maxPoolSize` | int | 20 | 最大线程数 |
| `queueCapacity` | int | 100 | 任务队列容量 |

**说明：**
- `corePoolSize`：线程池始终保持的线程数量，即使这些线程处于空闲状态
- `maxPoolSize`：线程池允许的最大线程数量，当队列满时会创建新线程直到达到此限制
- `queueCapacity`：等待执行的任务队列大小，当核心线程都忙碌时任务会进入队列等待

### 调度器配置（scheduler）

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `poolSize` | int | 10 | 调度器线程池大小 |
| `awaitTerminationSeconds` | int | 30 | 等待终止时间（秒） |

**说明：**
- `poolSize`：用于管理定时任务调度的线程数量
- `awaitTerminationSeconds`：应用关闭时等待调度器停止的最大时间

### 分布式锁配置（lock）

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `defaultTimeoutSeconds` | int | 30 | 默认锁超时时间（秒） |

**说明：**
- 防止死锁的自动释放时间，锁会在此时间后自动释放

### 监控配置（metrics）

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | boolean | true | 是否启用监控指标收集 |
| `pushIntervalSeconds` | int | 60 | 指标推送间隔（秒） |

**说明：**
- `enabled`：控制是否收集和推送监控指标
- `pushIntervalSeconds`：向N9E监控系统推送指标的频率

### 脚本安全配置（script）

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `allowedDirectories` | List<String> | ["/opt/app/scripts", "/usr/local/scripts", "/home/<USER>"] | 允许的脚本目录列表 |
| `strictMode` | boolean | true | 是否启用严格模式 |
| `maxExecutionTimeSeconds` | int | 3600 | 最大脚本执行时间（秒） |
| `maxFileSizeMB` | int | 10 | 最大脚本文件大小（MB） |

**说明：**
- `allowedDirectories`：只有这些目录中的脚本才能被执行，用于防止路径遍历攻击
- `strictMode`：启用更严格的安全检查，包括参数验证、环境变量检查等
- `maxExecutionTimeSeconds`：防止脚本无限运行的超时限制
- `maxFileSizeMB`：限制脚本文件的最大大小

## 任务配置（jobs）

### 基本任务配置

| 配置项 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `jobName` | String | 是 | 任务名称，必须唯一 |
| `type` | Enum | 是 | 任务类型：API 或 SCRIPT |
| `cron` | String | 是 | Cron表达式（6位格式） |
| `enabled` | boolean | 否 | 是否启用任务，默认true |
| `description` | String | 否 | 任务描述 |

**Cron表达式格式：**
```
秒 分 时 日 月 周
```

**示例：**
- `"0 0 1 * * ?"` - 每天凌晨1点
- `"0 */5 * * * ?"` - 每5分钟
- `"0 0 9 * * MON-FRI"` - 工作日上午9点
- `"0 0 0 1 * ?"` - 每月1号零点

### 重试和告警配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `retryCount` | int | 0 | 失败重试次数 |
| `retryIntervalSeconds` | int | 60 | 重试间隔（秒） |
| `alertThreshold` | int | 3 | 告警阈值（连续失败次数） |

**说明：**
- `retryCount`：任务失败后的重试次数，0表示不重试
- `retryIntervalSeconds`：每次重试之间的等待时间
- `alertThreshold`：连续失败多少次后触发告警日志

### API任务配置（apiConfig）

当`type`为`API`时，需要配置`apiConfig`：

| 配置项 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `targetUrl` | String | 是 | 目标URL |
| `httpMethod` | String | 是 | HTTP方法：GET, POST, PUT, DELETE, PATCH |
| `requestTimeoutSeconds` | int | 否 | 请求超时时间（秒），默认30 |
| `headers` | Map<String, String> | 否 | HTTP请求头 |
| `body` | String | 否 | 请求体（POST/PUT时使用） |

**示例：**
```yaml
apiConfig:
  targetUrl: "http://api.example.com/sync"
  httpMethod: "POST"
  requestTimeoutSeconds: 120
  headers:
    Content-Type: "application/json"
    Authorization: "Bearer ${api.token}"
  body: |
    {
      "action": "sync",
      "timestamp": "${current.timestamp}"
    }
```

### 脚本任务配置（scriptConfig）

当`type`为`SCRIPT`时，需要配置`scriptConfig`：

| 配置项 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `path` | String | 是 | 脚本文件路径（绝对路径） |
| `workingDirectory` | String | 否 | 工作目录 |
| `arguments` | List<String> | 否 | 脚本参数列表 |
| `timeoutSeconds` | int | 否 | 脚本超时时间（秒），默认300 |
| `environmentVariables` | Map<String, String> | 否 | 环境变量 |

**示例：**
```yaml
scriptConfig:
  path: "/opt/app/scripts/backup.sh"
  workingDirectory: "/opt/app/scripts"
  arguments:
    - "--target"
    - "/backup"
    - "--compress"
  timeoutSeconds: 1800
  environmentVariables:
    BACKUP_TYPE: "full"
    LOG_LEVEL: "info"
```

## 环境变量替换

配置文件支持环境变量替换，格式为`${变量名:默认值}`：

```yaml
# 使用环境变量，如果不存在则使用默认值
targetUrl: "${API_BASE_URL:http://localhost:8080}/api/sync"
Authorization: "Bearer ${API_TOKEN:default-token}"
```

**内置变量：**
- `${current.timestamp}` - 当前时间戳
- `${current.date}` - 当前日期（yyyy-MM-dd）
- `${current.datetime}` - 当前日期时间（yyyy-MM-dd HH:mm:ss）

## 配置验证规则

### 通用验证
- `jobName`：不能为空，不能重复，只能包含字母、数字、下划线和连字符
- `cron`：必须是有效的Cron表达式
- `retryCount`：必须大于等于0
- `retryIntervalSeconds`：必须大于0
- `alertThreshold`：必须大于0

### API任务验证
- `targetUrl`：必须是有效的URL格式
- `httpMethod`：必须是支持的HTTP方法
- `requestTimeoutSeconds`：必须大于0且小于3600

### 脚本任务验证
- `path`：必须是绝对路径，文件必须存在且可执行
- `path`：必须在允许的目录中
- `timeoutSeconds`：必须大于0且小于系统配置的最大执行时间
- `arguments`：不能包含危险字符（如`;`, `&`, `|`等）

## 最佳实践

### 1. 任务命名
- 使用描述性的名称：`sync-user-data` 而不是 `job1`
- 使用连字符分隔：`cleanup-temp-files`
- 避免使用特殊字符

### 2. Cron表达式
- 避免在同一时间调度太多任务
- 考虑系统负载，错开高峰时间
- 使用注释说明执行时间

### 3. 重试策略
- 对于幂等操作，可以设置较多重试次数
- 对于非幂等操作，谨慎设置重试
- 根据操作的重要性设置告警阈值

### 4. 超时设置
- API请求：根据接口响应时间设置合理超时
- 脚本执行：根据脚本复杂度设置超时时间
- 避免设置过长的超时时间

### 5. 安全考虑
- 脚本文件放在专门的目录中
- 避免在配置中硬编码敏感信息
- 使用环境变量存储密钥和令牌
- 定期审查脚本权限

### 6. 监控和日志
- 为重要任务设置合适的告警阈值
- 在任务描述中说明业务目的
- 定期检查任务执行历史

## 配置示例

### 完整的API任务示例
```yaml
- jobName: user-data-sync
  type: API
  cron: "0 0 2 * * ?"  # 每天凌晨2点
  enabled: true
  retryCount: 3
  retryIntervalSeconds: 300  # 5分钟
  alertThreshold: 2
  description: "同步用户数据到数据仓库"
  apiConfig:
    targetUrl: "${DATA_WAREHOUSE_URL}/api/v1/users/sync"
    httpMethod: "POST"
    requestTimeoutSeconds: 600  # 10分钟
    headers:
      Content-Type: "application/json"
      Authorization: "Bearer ${DW_API_TOKEN}"
      X-Request-ID: "${current.timestamp}"
    body: |
      {
        "syncType": "incremental",
        "since": "${last.sync.timestamp}",
        "batchSize": 1000
      }
```

### 完整的脚本任务示例
```yaml
- jobName: database-backup
  type: SCRIPT
  cron: "0 30 1 * * ?"  # 每天凌晨1点30分
  enabled: true
  retryCount: 2
  retryIntervalSeconds: 600  # 10分钟
  alertThreshold: 1  # 备份失败立即告警
  description: "每日数据库备份"
  scriptConfig:
    path: "/opt/backup/scripts/db-backup.sh"
    workingDirectory: "/opt/backup"
    arguments:
      - "--database"
      - "production"
      - "--output-dir"
      - "${BACKUP_DIR:/backup/db}"
      - "--compress"
      - "--verify"
    timeoutSeconds: 3600  # 1小时
    environmentVariables:
      DB_HOST: "${DB_HOST}"
      DB_USER: "${DB_BACKUP_USER}"
      DB_PASSWORD: "${DB_BACKUP_PASSWORD}"
      BACKUP_RETENTION_DAYS: "30"
      NOTIFICATION_EMAIL: "<EMAIL>"
```

## 故障排查

### 常见配置错误

1. **Cron表达式错误**
   ```
   错误：cron: "0 0 1 * *"     # 5位表达式
   正确：cron: "0 0 1 * * ?"   # 6位表达式
   ```

2. **脚本路径错误**
   ```
   错误：path: "scripts/backup.sh"        # 相对路径
   正确：path: "/opt/app/scripts/backup.sh" # 绝对路径
   ```

3. **重复的任务名称**
   ```yaml
   # 错误：两个任务使用相同名称
   - jobName: backup
   - jobName: backup  # 重复！
   ```

4. **无效的HTTP方法**
   ```
   错误：httpMethod: "PATCH"  # 不支持
   正确：httpMethod: "POST"   # 支持的方法
   ```

### 配置验证

启动时系统会自动验证配置文件，如果发现错误会在日志中输出详细信息：

```
ERROR - Job配置验证失败 [backup-job]: 脚本文件不存在: /invalid/path/script.sh
ERROR - Job配置验证失败 [api-job]: 无效的Cron表达式: 0 0 1 * *
```

### 调试技巧

1. **启用调试日志**
   ```properties
   logging.level.com.xylink.manager.job.config=DEBUG
   ```

2. **使用配置验证接口**
   ```bash
   curl -X POST http://localhost:8080/jobs/reload
   ```

3. **检查任务状态**
   ```bash
   curl http://localhost:8080/jobs/status
   ```

## 版本兼容性

| 配置版本 | 系统版本 | 变更说明 |
|----------|----------|----------|
| 1.0 | 1.0.0+ | 初始版本 |
| 1.1 | 1.1.0+ | 增加脚本安全配置 |
| 1.2 | 1.2.0+ | 增加监控配置 |

## 相关文档

- [Job调度系统架构设计](../scheduler/README.md)
- [安全配置指南](../security/README.md)
- [监控指标说明](../metrics/README.md)
- [API接口文档](../controller/README.md)