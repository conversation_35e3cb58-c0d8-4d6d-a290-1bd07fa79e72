package com.xylink.manager.job.logger;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.manager.job.model.JobExecutionResult;
import com.xylink.manager.job.util.JobMDCUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Job执行日志管理器
 * 负责记录Job执行过程中的各种日志，包括敏感数据脱敏功能
 */
@Slf4j
@Component
public class JobLogger {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 敏感字段模式，用于脱敏处理
     */
    private static final Pattern SENSITIVE_PATTERN = Pattern.compile(
        "(?i)(password|token|apikey|api_key|secret|authorization|idcard|id_card|phone|mobile|email)\\s*[:=]\\s*[\"']?([^\\s,}\"']+)",
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 记录Job开始执行
     * 
     * @param jobName 任务名称
     * @param executionId 执行ID
     */
    public void logJobStart(String jobName, String executionId) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "JOB_START");
        logData.put("jobName", jobName);
        logData.put("executionId", executionId);
        logData.put("timestamp", System.currentTimeMillis());
        
        log.info("Job execution started: {}", toJsonString(logData));
    }
    
    /**
     * 记录分布式锁获取情况
     * 
     * @param jobName 任务名称
     * @param acquired 是否获取成功
     */
    public void logLockAcquisition(String jobName, boolean acquired) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "LOCK_ACQUISITION");
        logData.put("jobName", jobName);
        logData.put("executionId", JobMDCUtil.getCurrentExecutionId());
        logData.put("acquired", acquired);
        logData.put("timestamp", System.currentTimeMillis());
        
        if (acquired) {
            log.info("Distributed lock acquired successfully: {}", toJsonString(logData));
        } else {
            log.info("Failed to acquire distributed lock, skipping execution: {}", toJsonString(logData));
        }
    }
    
    /**
     * 记录Job执行详情
     * 
     * @param jobName 任务名称
     * @param executionId 执行ID
     * @param details 执行详情（会进行敏感数据脱敏）
     */
    public void logJobExecution(String jobName, String executionId, Object details) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "JOB_EXECUTION");
        logData.put("jobName", jobName);
        logData.put("executionId", executionId);
        logData.put("details", sanitizeData(details));
        logData.put("timestamp", System.currentTimeMillis());
        
        log.info("Job execution details: {}", toJsonString(logData));
    }
    
    /**
     * 记录Job执行结果
     * 
     * @param jobName 任务名称
     * @param executionId 执行ID
     * @param result 执行结果
     */
    public void logJobResult(String jobName, String executionId, JobExecutionResult result) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "JOB_RESULT");
        logData.put("jobName", jobName);
        logData.put("executionId", executionId);
        logData.put("status", result.getStatus().name());
        logData.put("durationMs", result.getDurationMs());
        logData.put("retryCount", result.getRetryCount());
        logData.put("timestamp", System.currentTimeMillis());
        
        if (result.getErrorMessage() != null) {
            logData.put("errorMessage", sanitizeData(result.getErrorMessage()));
        }
        
        if (result.getDetails() != null) {
            logData.put("details", sanitizeData(result.getDetails()));
        }
        
        String logMessage = "Job execution completed: " + toJsonString(logData);
        
        switch (result.getStatus()) {
            case SUCCESS:
                log.info(logMessage);
                break;
            case FAILURE:
            case TIMEOUT:
                log.error(logMessage);
                break;
            default:
                log.info(logMessage);
        }
    }
    
    /**
     * 记录Job重试
     * 
     * @param jobName 任务名称
     * @param executionId 执行ID
     * @param retryCount 当前重试次数
     * @param maxRetries 最大重试次数
     * @param retryIntervalSeconds 重试间隔秒数
     */
    public void logRetry(String jobName, String executionId, int retryCount, int maxRetries, int retryIntervalSeconds) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "JOB_RETRY");
        logData.put("jobName", jobName);
        logData.put("executionId", executionId);
        logData.put("retryCount", retryCount);
        logData.put("maxRetries", maxRetries);
        logData.put("retryIntervalSeconds", retryIntervalSeconds);
        logData.put("timestamp", System.currentTimeMillis());
        
        log.warn("Job execution failed, retrying ({}/{}): {}", retryCount, maxRetries, toJsonString(logData));
    }
    
    /**
     * 记录告警日志
     * 
     * @param jobName 任务名称
     * @param consecutiveFailures 连续失败次数
     * @param alertThreshold 告警阈值
     */
    public void logAlert(String jobName, int consecutiveFailures, int alertThreshold) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "JOB_ALERT");
        logData.put("jobName", jobName);
        logData.put("consecutiveFailures", consecutiveFailures);
        logData.put("alertThreshold", alertThreshold);
        logData.put("timestamp", System.currentTimeMillis());
        
        log.error("JOB ALERT: Job '{}' has failed {} consecutive times, exceeding alert threshold of {}: {}", 
                jobName, consecutiveFailures, alertThreshold, toJsonString(logData));
    }
    
    /**
     * 记录锁释放
     * 
     * @param jobName 任务名称
     * @param executionId 执行ID
     * @param released 是否释放成功
     */
    public void logLockRelease(String jobName, String executionId, boolean released) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "LOCK_RELEASE");
        logData.put("jobName", jobName);
        logData.put("executionId", executionId);
        logData.put("released", released);
        logData.put("timestamp", System.currentTimeMillis());
        
        if (released) {
            log.info("Distributed lock released successfully: {}", toJsonString(logData));
        } else {
            log.warn("Failed to release distributed lock: {}", toJsonString(logData));
        }
    }
    
    /**
     * 记录API调用详情
     * 
     * @param jobName 任务名称
     * @param executionId 执行ID
     * @param method HTTP方法
     * @param url 请求URL
     * @param statusCode 响应状态码
     * @param responseBody 响应体（会进行脱敏）
     * @param durationMs 请求耗时
     */
    public void logApiCall(String jobName, String executionId, String method, String url, 
                          int statusCode, String responseBody, long durationMs) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "API_CALL");
        logData.put("jobName", jobName);
        logData.put("executionId", executionId);
        logData.put("method", method);
        logData.put("url", url);
        logData.put("statusCode", statusCode);
        logData.put("durationMs", durationMs);
        logData.put("timestamp", System.currentTimeMillis());
        
        if (responseBody != null) {
            logData.put("responseBody", sanitizeData(responseBody));
        }
        
        log.info("API call completed: {}", toJsonString(logData));
    }
    
    /**
     * 记录脚本执行详情
     * 
     * @param jobName 任务名称
     * @param executionId 执行ID
     * @param scriptPath 脚本路径
     * @param exitCode 退出码
     * @param stdout 标准输出（会进行脱敏）
     * @param stderr 标准错误输出（会进行脱敏）
     * @param durationMs 执行耗时
     */
    public void logScriptExecution(String jobName, String executionId, String scriptPath, 
                                 int exitCode, String stdout, String stderr, long durationMs) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "SCRIPT_EXECUTION");
        logData.put("jobName", jobName);
        logData.put("executionId", executionId);
        logData.put("scriptPath", scriptPath);
        logData.put("exitCode", exitCode);
        logData.put("durationMs", durationMs);
        logData.put("timestamp", System.currentTimeMillis());
        
        if (stdout != null && !stdout.trim().isEmpty()) {
            logData.put("stdout", sanitizeData(stdout));
        }
        
        if (stderr != null && !stderr.trim().isEmpty()) {
            logData.put("stderr", sanitizeData(stderr));
        }
        
        log.info("Script execution completed: {}", toJsonString(logData));
    }
    
    /**
     * 敏感数据脱敏处理
     * 
     * @param data 原始数据
     * @return 脱敏后的数据
     */
    public String sanitizeData(Object data) {
        if (data == null) {
            return null;
        }
        
        String dataStr = data.toString();
        
        // 使用正则表达式替换敏感字段
        return SENSITIVE_PATTERN.matcher(dataStr).replaceAll("$1: ***MASKED***");
    }
    
    /**
     * 将对象转换为JSON字符串
     * 
     * @param obj 对象
     * @return JSON字符串
     */
    private String toJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.warn("Failed to convert object to JSON: {}", e.getMessage());
            return obj.toString();
        }
    }
}