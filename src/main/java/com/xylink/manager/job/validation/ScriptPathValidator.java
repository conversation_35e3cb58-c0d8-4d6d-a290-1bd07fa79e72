package com.xylink.manager.job.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 脚本路径验证器
 */
public class ScriptPathValidator implements ConstraintValidator<ValidScriptPath, String> {
    
    @Override
    public void initialize(ValidScriptPath constraintAnnotation) {
        // 初始化方法
    }
    
    @Override
    public boolean isValid(String scriptPath, ConstraintValidatorContext context) {
        if (scriptPath == null || scriptPath.trim().isEmpty()) {
            return true; // null和空值由@NotBlank注解处理
        }
        
        try {
            // 检查路径是否包含危险字符
            if (containsDangerousCharacters(scriptPath)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("脚本路径包含危险字符")
                        .addConstraintViolation();
                return false;
            }
            
            // 检查是否为绝对路径
            Path path = Paths.get(scriptPath);
            if (!path.isAbsolute()) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("脚本路径必须是绝对路径")
                        .addConstraintViolation();
                return false;
            }
            
            // 检查路径是否在安全目录范围内（可配置）
            if (!isInSafeDirectory(scriptPath)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("脚本路径不在安全目录范围内")
                        .addConstraintViolation();
                return false;
            }
            
            return true;
        } catch (Exception e) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("脚本路径格式无效: " + e.getMessage())
                    .addConstraintViolation();
            return false;
        }
    }
    
    /**
     * 检查路径是否包含危险字符
     */
    private boolean containsDangerousCharacters(String path) {
        // 检查路径遍历攻击
        if (path.contains("..") || path.contains("./") || path.contains(".\\")) {
            return true;
        }
        
        // 检查其他危险字符
        String[] dangerousChars = {";", "&", "|", "`", "$", "(", ")", "{", "}", "[", "]"};
        for (String dangerousChar : dangerousChars) {
            if (path.contains(dangerousChar)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查路径是否在安全目录范围内
     * 这里可以根据实际需求配置安全目录列表
     */
    private boolean isInSafeDirectory(String scriptPath) {
        // 默认安全目录列表（可以通过配置文件配置）
        String[] safeDirectories = {
            "/opt/scripts/",
            "/usr/local/scripts/",
            "/home/<USER>/",
            "/var/scripts/"
        };
        
        for (String safeDir : safeDirectories) {
            if (scriptPath.startsWith(safeDir)) {
                return true;
            }
        }
        
        // 如果没有配置安全目录，则允许所有绝对路径（在生产环境中应该配置）
        return true;
    }
}