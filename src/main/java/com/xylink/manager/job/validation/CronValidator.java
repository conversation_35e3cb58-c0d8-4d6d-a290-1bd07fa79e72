package com.xylink.manager.job.validation;

import org.springframework.scheduling.support.CronExpression;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Cron表达式验证器
 */
public class CronValidator implements ConstraintValidator<ValidCron, String> {
    
    @Override
    public void initialize(ValidCron constraintAnnotation) {
        // 初始化方法
    }
    
    @Override
    public boolean isValid(String cron, ConstraintValidatorContext context) {
        if (cron == null || cron.trim().isEmpty()) {
            return true; // null和空值由@NotBlank注解处理
        }
        
        try {
            // 使用Spring的CronExpression来验证Cron表达式
            CronExpression.parse(cron);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}