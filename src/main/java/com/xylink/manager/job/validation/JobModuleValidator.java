package com.xylink.manager.job.validation;

import com.xylink.manager.job.config.JobConfigLoader;
import com.xylink.manager.job.controller.JobController;
import com.xylink.manager.job.executor.JobExecutor;
import com.xylink.manager.job.lock.JobDistributedLock;
import com.xylink.manager.job.metrics.JobMetrics;
import com.xylink.manager.job.model.JobConfig;
import com.xylink.manager.job.scheduler.JobScheduler;
import com.xylink.manager.job.service.JobStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Job模块验证器
 * 在应用启动时验证Job模块的核心功能是否正常
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class JobModuleValidator implements ApplicationRunner {

    @Autowired
    private JobConfigLoader jobConfigLoader;

    @Autowired
    private JobScheduler jobScheduler;

    @Autowired
    private JobExecutor jobExecutor;

    @Autowired
    private JobStatusService jobStatusService;

    @Autowired
    private JobDistributedLock jobDistributedLock;

    @Autowired
    private JobMetrics jobMetrics;

    @Autowired
    private JobController jobController;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始验证Job模块功能...");

        try {
            // 1. 验证配置加载器
            validateJobConfigLoader();

            // 2. 验证调度器
            validateJobScheduler();

            // 3. 验证分布式锁
            validateDistributedLock();

            // 4. 验证状态服务
            validateStatusService();

            // 5. 验证监控指标
            validateMetrics();

            log.info("✅ Job模块功能验证完成，所有核心组件正常工作");

        } catch (Exception e) {
            log.error("❌ Job模块功能验证失败", e);
            throw e;
        }
    }

    private void validateJobConfigLoader() {
        try {
            List<JobConfig> configs = jobConfigLoader.loadJobConfigs();
            if (configs.isEmpty()) {
                log.info("⚠️ JobConfigLoader验证通过，但未加载到任何Job配置，Job模块将以禁用模式运行");
            } else {
                log.info("✅ JobConfigLoader验证通过，成功加载{}个Job配置", configs.size());
            }
        } catch (Exception e) {
            log.warn("⚠️ JobConfigLoader验证失败，Job模块将以禁用模式运行: {}", e.getMessage());
            // 不抛出异常，允许应用继续启动
        }
    }

    private void validateJobScheduler() {
        try {
            boolean initialized = jobScheduler.isInitialized();
            int scheduledCount = jobScheduler.getScheduledJobCount();
            String status = jobScheduler.getSchedulerStatus();
            
            log.info("✅ JobScheduler验证通过，初始化状态: {}, 调度任务数: {}, 状态: {}", 
                    initialized, scheduledCount, status);
        } catch (Exception e) {
            log.error("❌ JobScheduler验证失败", e);
            throw new RuntimeException("JobScheduler验证失败", e);
        }
    }

    private void validateDistributedLock() {
        try {
            String testJobName = "test-validation-job";
            String lockMode = jobDistributedLock.getLockMode();

            // 测试锁获取和释放
            boolean acquired = jobDistributedLock.tryLock(testJobName);
            if (acquired) {
                boolean isLocked = jobDistributedLock.isLocked(testJobName);
                boolean isHeldByCurrentThread = jobDistributedLock.isHeldByCurrentThread(testJobName);
                jobDistributedLock.releaseLock(testJobName);

                log.info("✅ JobDistributedLock验证通过，模式: {}, 锁获取: {}, 锁状态: {}, 当前线程持有: {}",
                        lockMode, acquired, isLocked, isHeldByCurrentThread);
            } else {
                log.warn("⚠️ JobDistributedLock验证：无法获取测试锁，模式: {}", lockMode);
            }
        } catch (Exception e) {
            log.warn("⚠️ JobDistributedLock验证失败，但不影响应用启动: {}", e.getMessage());
            // 不抛出异常，允许应用继续启动
        }
    }

    private void validateStatusService() {
        try {
            List<JobConfig> configs = jobStatusService.getAllJobConfigs();
            log.info("✅ JobStatusService验证通过，获取到{}个Job配置", configs.size());
        } catch (Exception e) {
            log.warn("⚠️ JobStatusService验证失败，但不影响应用启动: {}", e.getMessage());
            // 不抛出异常，允许应用继续启动
        }
    }

    private void validateMetrics() {
        try {
            boolean n9eAvailable = jobMetrics.isN9eAvailable();
            log.info("✅ JobMetrics验证通过，N9E可用性: {}", n9eAvailable);
        } catch (Exception e) {
            log.warn("⚠️ JobMetrics验证失败，但不影响应用启动: {}", e.getMessage());
            // 不抛出异常，允许应用继续启动
        }
    }
}