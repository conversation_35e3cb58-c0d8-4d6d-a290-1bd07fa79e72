package com.xylink.manager.job.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * JobConfig业务逻辑验证注解
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = JobConfigValidator.class)
@Documented
public @interface ValidJobConfig {
    
    String message() default "JobConfig配置不符合业务规则";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}