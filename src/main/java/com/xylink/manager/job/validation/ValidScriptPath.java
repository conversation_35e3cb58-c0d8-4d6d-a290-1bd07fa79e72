package com.xylink.manager.job.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 脚本路径验证注解
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ScriptPathValidator.class)
@Documented
public @interface ValidScriptPath {
    
    String message() default "脚本路径不安全或不存在";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}