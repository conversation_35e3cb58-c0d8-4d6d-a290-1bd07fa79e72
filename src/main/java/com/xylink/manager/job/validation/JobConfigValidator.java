package com.xylink.manager.job.validation;

import com.xylink.manager.job.model.JobConfig;
import com.xylink.manager.job.model.JobType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * JobConfig业务逻辑验证器
 */
public class JobConfigValidator implements ConstraintValidator<ValidJobConfig, JobConfig> {
    
    @Override
    public void initialize(ValidJobConfig constraintAnnotation) {
        // 初始化方法，可以获取注解参数
    }
    
    @Override
    public boolean isValid(JobConfig jobConfig, ConstraintValidatorContext context) {
        if (jobConfig == null) {
            return true; // null值由@NotNull注解处理
        }
        
        boolean isValid = true;
        
        // 验证API类型Job必须有ApiConfig
        if (JobType.API.equals(jobConfig.getType())) {
            if (jobConfig.getApiConfig() == null) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("API类型的Job必须配置apiConfig")
                        .addPropertyNode("apiConfig")
                        .addConstraintViolation();
                isValid = false;
            }
            
            // API类型Job不应该有ScriptConfig
            if (jobConfig.getScriptConfig() != null) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("API类型的Job不应该配置scriptConfig")
                        .addPropertyNode("scriptConfig")
                        .addConstraintViolation();
                isValid = false;
            }
        }
        
        // 验证SCRIPT类型Job必须有ScriptConfig
        if (JobType.SCRIPT.equals(jobConfig.getType())) {
            if (jobConfig.getScriptConfig() == null) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("SCRIPT类型的Job必须配置scriptConfig")
                        .addPropertyNode("scriptConfig")
                        .addConstraintViolation();
                isValid = false;
            }
            
            // SCRIPT类型Job不应该有ApiConfig
            if (jobConfig.getApiConfig() != null) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("SCRIPT类型的Job不应该配置apiConfig")
                        .addPropertyNode("apiConfig")
                        .addConstraintViolation();
                isValid = false;
            }
        }
        
        return isValid;
    }
}