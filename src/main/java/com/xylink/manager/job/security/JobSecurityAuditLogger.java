package com.xylink.manager.job.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.manager.job.util.JobLoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Job安全审计日志管理器
 * 负责记录Job相关的安全事件和管理操作
 */
@Slf4j
@Component
public class JobSecurityAuditLogger {

    /**
     * 安全审计专用日志记录器
     */
    private static final Logger SECURITY_AUDIT_LOGGER = LoggerFactory.getLogger("com.xylink.manager.job.security.audit");

    /**
     * 管理操作审计日志记录器
     */
    private static final Logger MANAGEMENT_AUDIT_LOGGER = LoggerFactory.getLogger("com.xylink.manager.job.management.audit");

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    /**
     * 记录脚本安全验证事件
     */
    public void logScriptSecurityValidation(String jobName, String executionId, String scriptPath, 
                                          String validationType, boolean success, String details) {
        Map<String, Object> auditData = createBaseAuditData("SCRIPT_SECURITY_VALIDATION", jobName, executionId);
        auditData.put("scriptPath", JobLoggerUtil.sanitizeData(scriptPath));
        auditData.put("validationType", validationType);
        auditData.put("success", success);
        auditData.put("details", JobLoggerUtil.sanitizeData(details));

        if (success) {
            SECURITY_AUDIT_LOGGER.info("脚本安全验证通过: {}", formatAuditData(auditData));
        } else {
            SECURITY_AUDIT_LOGGER.warn("脚本安全验证失败: {}", formatAuditData(auditData));
        }
    }

    /**
     * 记录命令注入检测事件
     */
    public void logCommandInjectionDetection(String jobName, String executionId, String inputType, 
                                           String suspiciousInput, String detectionReason) {
        Map<String, Object> auditData = createBaseAuditData("COMMAND_INJECTION_DETECTION", jobName, executionId);
        auditData.put("inputType", inputType);
        auditData.put("suspiciousInput", JobLoggerUtil.sanitizeData(suspiciousInput));
        auditData.put("detectionReason", detectionReason);

        SECURITY_AUDIT_LOGGER.error("检测到潜在命令注入攻击: {}", formatAuditData(auditData));
    }

    /**
     * 记录路径遍历攻击检测事件
     */
    public void logPathTraversalDetection(String jobName, String executionId, String suspiciousPath, String detectionReason) {
        Map<String, Object> auditData = createBaseAuditData("PATH_TRAVERSAL_DETECTION", jobName, executionId);
        auditData.put("suspiciousPath", JobLoggerUtil.sanitizeData(suspiciousPath));
        auditData.put("detectionReason", detectionReason);

        SECURITY_AUDIT_LOGGER.error("检测到路径遍历攻击: {}", formatAuditData(auditData));
    }

    /**
     * 记录权限违规事件
     */
    public void logPermissionViolation(String jobName, String executionId, String operation, 
                                     String resource, String reason) {
        Map<String, Object> auditData = createBaseAuditData("PERMISSION_VIOLATION", jobName, executionId);
        auditData.put("operation", operation);
        auditData.put("resource", JobLoggerUtil.sanitizeData(resource));
        auditData.put("reason", reason);

        SECURITY_AUDIT_LOGGER.error("权限违规: {}", formatAuditData(auditData));
    }

    /**
     * 记录Job管理操作
     */
    public void logJobManagementOperation(String operation, String jobName, String operator, 
                                        Map<String, Object> operationDetails, boolean success, String result) {
        Map<String, Object> auditData = createBaseManagementAuditData(operation, jobName, operator);
        auditData.put("operationDetails", JobLoggerUtil.sanitizeData(operationDetails));
        auditData.put("success", success);
        auditData.put("result", JobLoggerUtil.sanitizeData(result));

        if (success) {
            MANAGEMENT_AUDIT_LOGGER.info("Job管理操作成功: {}", formatAuditData(auditData));
        } else {
            MANAGEMENT_AUDIT_LOGGER.warn("Job管理操作失败: {}", formatAuditData(auditData));
        }
    }

    /**
     * 记录配置变更操作
     */
    public void logConfigurationChange(String operation, String configType, String operator, 
                                     Object oldValue, Object newValue, boolean success, String result) {
        Map<String, Object> auditData = createBaseManagementAuditData(operation, null, operator);
        auditData.put("configType", configType);
        auditData.put("oldValue", JobLoggerUtil.sanitizeData(oldValue));
        auditData.put("newValue", JobLoggerUtil.sanitizeData(newValue));
        auditData.put("success", success);
        auditData.put("result", result);

        if (success) {
            MANAGEMENT_AUDIT_LOGGER.info("配置变更成功: {}", formatAuditData(auditData));
        } else {
            MANAGEMENT_AUDIT_LOGGER.warn("配置变更失败: {}", formatAuditData(auditData));
        }
    }

    /**
     * 记录敏感数据访问
     */
    public void logSensitiveDataAccess(String jobName, String executionId, String dataType, 
                                     String accessType, String accessor, boolean authorized) {
        Map<String, Object> auditData = createBaseAuditData("SENSITIVE_DATA_ACCESS", jobName, executionId);
        auditData.put("dataType", dataType);
        auditData.put("accessType", accessType);
        auditData.put("accessor", accessor);
        auditData.put("authorized", authorized);

        if (authorized) {
            SECURITY_AUDIT_LOGGER.info("敏感数据访问: {}", formatAuditData(auditData));
        } else {
            SECURITY_AUDIT_LOGGER.warn("未授权的敏感数据访问: {}", formatAuditData(auditData));
        }
    }

    /**
     * 记录异常的Job执行行为
     */
    public void logAnomalousJobBehavior(String jobName, String executionId, String behaviorType, 
                                      String description, String severity) {
        Map<String, Object> auditData = createBaseAuditData("ANOMALOUS_JOB_BEHAVIOR", jobName, executionId);
        auditData.put("behaviorType", behaviorType);
        auditData.put("description", description);
        auditData.put("severity", severity);

        switch (severity.toUpperCase()) {
            case "HIGH":
                SECURITY_AUDIT_LOGGER.error("检测到高风险异常行为: {}", formatAuditData(auditData));
                break;
            case "MEDIUM":
                SECURITY_AUDIT_LOGGER.warn("检测到中风险异常行为: {}", formatAuditData(auditData));
                break;
            default:
                SECURITY_AUDIT_LOGGER.info("检测到异常行为: {}", formatAuditData(auditData));
                break;
        }
    }

    /**
     * 记录资源使用异常
     */
    public void logResourceUsageAnomaly(String jobName, String executionId, String resourceType, 
                                      long currentUsage, long threshold, String action) {
        Map<String, Object> auditData = createBaseAuditData("RESOURCE_USAGE_ANOMALY", jobName, executionId);
        auditData.put("resourceType", resourceType);
        auditData.put("currentUsage", currentUsage);
        auditData.put("threshold", threshold);
        auditData.put("action", action);

        SECURITY_AUDIT_LOGGER.warn("资源使用异常: {}", formatAuditData(auditData));
    }

    /**
     * 记录锁操作审计
     */
    public void logLockOperation(String jobName, String executionId, String operation, 
                               boolean success, long duration, String details) {
        Map<String, Object> auditData = createBaseAuditData("LOCK_OPERATION", jobName, executionId);
        auditData.put("operation", operation);
        auditData.put("success", success);
        auditData.put("durationMs", duration);
        auditData.put("details", details);

        if (success) {
            SECURITY_AUDIT_LOGGER.debug("锁操作成功: {}", formatAuditData(auditData));
        } else {
            SECURITY_AUDIT_LOGGER.warn("锁操作失败: {}", formatAuditData(auditData));
        }
    }

    /**
     * 记录数据脱敏操作
     */
    public void logDataSanitization(String context, String dataType, int originalLength, 
                                  int sanitizedLength, String sanitizationRules) {
        Map<String, Object> auditData = new HashMap<>();
        auditData.put("timestamp", LocalDateTime.now().format(dateTimeFormatter));
        auditData.put("eventType", "DATA_SANITIZATION");
        auditData.put("context", context);
        auditData.put("dataType", dataType);
        auditData.put("originalLength", originalLength);
        auditData.put("sanitizedLength", sanitizedLength);
        auditData.put("sanitizationRules", sanitizationRules);

        SECURITY_AUDIT_LOGGER.debug("数据脱敏操作: {}", formatAuditData(auditData));
    }

    /**
     * 创建基础审计数据
     */
    private Map<String, Object> createBaseAuditData(String eventType, String jobName, String executionId) {
        Map<String, Object> auditData = new HashMap<>();
        auditData.put("timestamp", LocalDateTime.now().format(dateTimeFormatter));
        auditData.put("eventType", eventType);
        auditData.put("jobName", jobName);
        auditData.put("executionId", executionId);
        auditData.put("nodeId", getCurrentNodeId());
        auditData.put("threadName", Thread.currentThread().getName());
        
        // 添加MDC上下文信息
        Map<String, String> mdcContext = MDC.getCopyOfContextMap();
        if (mdcContext != null && !mdcContext.isEmpty()) {
            auditData.put("mdcContext", mdcContext);
        }
        
        return auditData;
    }

    /**
     * 创建基础管理审计数据
     */
    private Map<String, Object> createBaseManagementAuditData(String operation, String jobName, String operator) {
        Map<String, Object> auditData = new HashMap<>();
        auditData.put("timestamp", LocalDateTime.now().format(dateTimeFormatter));
        auditData.put("operation", operation);
        auditData.put("jobName", jobName);
        auditData.put("operator", operator);
        auditData.put("nodeId", getCurrentNodeId());
        auditData.put("sessionId", getSessionId());
        auditData.put("clientIp", getClientIp());
        
        return auditData;
    }

    /**
     * 格式化审计数据为JSON字符串
     */
    private String formatAuditData(Map<String, Object> auditData) {
        try {
            return objectMapper.writeValueAsString(auditData);
        } catch (Exception e) {
            log.warn("格式化审计数据失败", e);
            return auditData.toString();
        }
    }

    /**
     * 获取当前节点ID
     */
    private String getCurrentNodeId() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown-node";
        }
    }

    /**
     * 获取会话ID（从MDC或请求上下文中获取）
     */
    private String getSessionId() {
        String sessionId = MDC.get("sessionId");
        return sessionId != null ? sessionId : "unknown-session";
    }

    /**
     * 获取客户端IP（从MDC或请求上下文中获取）
     */
    private String getClientIp() {
        String clientIp = MDC.get("clientIp");
        return clientIp != null ? clientIp : "unknown-ip";
    }

    /**
     * 记录系统启动/关闭事件
     */
    public void logSystemEvent(String eventType, String details) {
        Map<String, Object> auditData = new HashMap<>();
        auditData.put("timestamp", LocalDateTime.now().format(dateTimeFormatter));
        auditData.put("eventType", eventType);
        auditData.put("details", details);
        auditData.put("nodeId", getCurrentNodeId());

        SECURITY_AUDIT_LOGGER.info("系统事件: {}", formatAuditData(auditData));
    }

    /**
     * 记录批量操作审计
     */
    public void logBatchOperation(String operation, int totalCount, int successCount, 
                                int failureCount, long durationMs, String operator) {
        Map<String, Object> auditData = createBaseManagementAuditData(operation, null, operator);
        auditData.put("totalCount", totalCount);
        auditData.put("successCount", successCount);
        auditData.put("failureCount", failureCount);
        auditData.put("durationMs", durationMs);
        auditData.put("successRate", totalCount > 0 ? (double) successCount / totalCount : 0.0);

        MANAGEMENT_AUDIT_LOGGER.info("批量操作完成: {}", formatAuditData(auditData));
    }
}