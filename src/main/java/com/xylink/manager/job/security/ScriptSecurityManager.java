package com.xylink.manager.job.security;

import com.xylink.manager.job.exception.JobExecutionException;
import com.xylink.manager.job.model.JobSystemConfig;
import com.xylink.manager.job.service.JobConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 脚本执行安全管理器
 * 负责脚本路径验证、命令注入防护、权限控制等安全功能
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class ScriptSecurityManager {

    @Autowired
    private JobConfigService jobConfigService;

    @Autowired
    private JobSecurityAuditLogger auditLogger;

    /**
     * 允许的脚本目录列表（从配置中获取）
     */
    private Set<String> allowedScriptDirectories;

    /**
     * 危险命令字符模式
     */
    private static final Pattern DANGEROUS_COMMAND_PATTERN = Pattern.compile(
            "[;&|`$()\\[\\]{}\\\\<>\"'\\n\\r\\t]|&&|\\|\\||>>|<<|\\$\\(|\\$\\{|`.*`"
    );

    /**
     * 危险文件扩展名
     */
    private static final Set<String> DANGEROUS_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".exe", ".bat", ".cmd", ".com", ".scr", ".pif", ".vbs", ".js", ".jar"
    ));

    /**
     * 允许的脚本扩展名
     */
    private static final Set<String> ALLOWED_SCRIPT_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".sh", ".py", ".pl", ".rb", ".php", ".lua"
    ));

    /**
     * 最大脚本文件大小（字节）
     */
    private static final long MAX_SCRIPT_FILE_SIZE = 10 * 1024 * 1024; // 10MB

    /**
     * 最大参数长度
     */
    private static final int MAX_ARGUMENT_LENGTH = 1000;

    /**
     * 最大参数数量
     */
    private static final int MAX_ARGUMENT_COUNT = 50;

    @PostConstruct
    public void init() {
        loadSecurityConfiguration();
        log.info("ScriptSecurityManager初始化完成，允许的脚本目录: {}", allowedScriptDirectories);
    }

    /**
     * 加载安全配置
     */
    private void loadSecurityConfiguration() {
        try {
            JobSystemConfig systemConfig = jobConfigService.getSystemConfig();
            
            // 从配置中获取允许的脚本目录，如果没有配置则使用默认值
            if (systemConfig.getScript() != null && 
                systemConfig.getScript().getAllowedDirectories() != null) {
                allowedScriptDirectories = new HashSet<>(systemConfig.getScript().getAllowedDirectories());
            } else {
                // 默认允许的脚本目录
                allowedScriptDirectories = new HashSet<>(Arrays.asList(
                        "/opt/app/scripts",
                        "/usr/local/scripts",
                        "/home/<USER>"
                ));
            }
            
        } catch (Exception e) {
            log.warn("加载脚本安全配置失败，使用默认配置", e);
            allowedScriptDirectories = new HashSet<>(Arrays.asList(
                    "/opt/app/scripts",
                    "/usr/local/scripts", 
                    "/home/<USER>"
            ));
        }
    }

    /**
     * 验证脚本路径安全性
     */
    public void validateScriptPath(String jobName, String executionId, String scriptPath) throws JobExecutionException {
        if (!StringUtils.hasText(scriptPath)) {
            throw new JobExecutionException(jobName, executionId, "脚本路径不能为空");
        }

        // 1. 基本路径安全检查
        validateBasicPathSecurity(jobName, executionId, scriptPath);

        // 2. 文件存在性和权限检查
        File scriptFile = validateFileExistence(jobName, executionId, scriptPath);

        // 3. 路径白名单检查
        validatePathWhitelist(jobName, executionId, scriptFile);

        // 4. 文件扩展名检查
        validateFileExtension(jobName, executionId, scriptFile);

        // 5. 文件大小检查
        validateFileSize(jobName, executionId, scriptFile);

        auditLogger.logScriptSecurityValidation(jobName, executionId, scriptFile.getAbsolutePath(), 
                "PATH_VALIDATION", true, "脚本路径安全验证通过");
        log.debug("脚本路径安全验证通过: jobName={}, executionId={}, path={}", 
                jobName, executionId, scriptFile.getAbsolutePath());
    }

    /**
     * 验证基本路径安全性
     */
    private void validateBasicPathSecurity(String jobName, String executionId, String scriptPath) throws JobExecutionException {
        // 检查路径遍历攻击
        if (scriptPath.contains("..") || scriptPath.contains("~") || scriptPath.contains("./")) {
            auditLogger.logPathTraversalDetection(jobName, executionId, scriptPath, 
                    "脚本路径包含路径遍历字符");
            throw new JobExecutionException(jobName, executionId, 
                    "脚本路径包含不安全的路径模式: " + scriptPath);
        }

        // 检查绝对路径
        if (!Paths.get(scriptPath).isAbsolute()) {
            throw new JobExecutionException(jobName, executionId, 
                    "脚本路径必须是绝对路径: " + scriptPath);
        }

        // 检查危险字符
        if (DANGEROUS_COMMAND_PATTERN.matcher(scriptPath).find()) {
            auditLogger.logCommandInjectionDetection(jobName, executionId, "scriptPath", 
                    scriptPath, "脚本路径包含危险字符");
            throw new JobExecutionException(jobName, executionId, 
                    "脚本路径包含危险字符: " + scriptPath);
        }

        // 检查路径长度
        if (scriptPath.length() > 500) {
            throw new JobExecutionException(jobName, executionId, 
                    "脚本路径过长: " + scriptPath.length());
        }
    }

    /**
     * 验证文件存在性和权限
     */
    private File validateFileExistence(String jobName, String executionId, String scriptPath) throws JobExecutionException {
        File scriptFile = new File(scriptPath);

        // 获取规范路径
        String canonicalPath;
        try {
            canonicalPath = scriptFile.getCanonicalPath();
        } catch (IOException e) {
            throw new JobExecutionException(jobName, executionId, 
                    "无法获取脚本文件的规范路径: " + scriptPath, e);
        }

        // 重新创建File对象使用规范路径
        scriptFile = new File(canonicalPath);

        // 检查文件是否存在
        if (!scriptFile.exists()) {
            throw new JobExecutionException(jobName, executionId, 
                    "脚本文件不存在: " + canonicalPath);
        }

        // 检查是否为文件
        if (!scriptFile.isFile()) {
            throw new JobExecutionException(jobName, executionId, 
                    "脚本路径不是文件: " + canonicalPath);
        }

        // 检查文件是否可读
        if (!scriptFile.canRead()) {
            throw new JobExecutionException(jobName, executionId, 
                    "脚本文件不可读: " + canonicalPath);
        }

        // 检查文件是否可执行
        if (!scriptFile.canExecute()) {
            throw new JobExecutionException(jobName, executionId, 
                    "脚本文件不可执行: " + canonicalPath);
        }

        return scriptFile;
    }

    /**
     * 验证路径白名单
     */
    private void validatePathWhitelist(String jobName, String executionId, File scriptFile) throws JobExecutionException {
        String canonicalPath = scriptFile.getAbsolutePath();
        
        boolean isAllowed = false;
        for (String allowedDir : allowedScriptDirectories) {
            try {
                Path allowedPath = Paths.get(allowedDir).toRealPath();
                Path scriptPath = Paths.get(canonicalPath);
                
                if (scriptPath.startsWith(allowedPath)) {
                    isAllowed = true;
                    break;
                }
            } catch (IOException e) {
                log.warn("检查允许目录时发生异常: allowedDir={}, scriptPath={}", 
                        allowedDir, canonicalPath, e);
            }
        }

        if (!isAllowed) {
            auditLogger.logPermissionViolation(jobName, executionId, "SCRIPT_EXECUTION", 
                    canonicalPath, "脚本文件不在允许的目录中");
            throw new JobExecutionException(jobName, executionId, 
                    String.format("脚本文件不在允许的目录中: %s, 允许的目录: %s", 
                            canonicalPath, allowedScriptDirectories));
        }
    }

    /**
     * 验证文件扩展名
     */
    private void validateFileExtension(String jobName, String executionId, File scriptFile) throws JobExecutionException {
        String fileName = scriptFile.getName().toLowerCase();
        
        // 检查危险扩展名
        for (String dangerousExt : DANGEROUS_EXTENSIONS) {
            if (fileName.endsWith(dangerousExt)) {
                throw new JobExecutionException(jobName, executionId, 
                        "不允许执行的文件类型: " + dangerousExt);
            }
        }

        // 检查是否为允许的脚本扩展名
        boolean hasAllowedExtension = false;
        for (String allowedExt : ALLOWED_SCRIPT_EXTENSIONS) {
            if (fileName.endsWith(allowedExt)) {
                hasAllowedExtension = true;
                break;
            }
        }

        // 如果没有扩展名，也允许（可能是shell脚本）
        if (!hasAllowedExtension && fileName.contains(".")) {
            throw new JobExecutionException(jobName, executionId, 
                    String.format("不支持的脚本文件扩展名，允许的扩展名: %s", ALLOWED_SCRIPT_EXTENSIONS));
        }
    }

    /**
     * 验证文件大小
     */
    private void validateFileSize(String jobName, String executionId, File scriptFile) throws JobExecutionException {
        long fileSize = scriptFile.length();
        if (fileSize > MAX_SCRIPT_FILE_SIZE) {
            throw new JobExecutionException(jobName, executionId, 
                    String.format("脚本文件过大: %d bytes, 最大允许: %d bytes", 
                            fileSize, MAX_SCRIPT_FILE_SIZE));
        }
    }

    /**
     * 验证脚本参数安全性
     */
    public void validateScriptArguments(String jobName, String executionId, List<String> arguments) throws JobExecutionException {
        if (arguments == null || arguments.isEmpty()) {
            return;
        }

        // 检查参数数量
        if (arguments.size() > MAX_ARGUMENT_COUNT) {
            throw new JobExecutionException(jobName, executionId, 
                    String.format("脚本参数过多: %d, 最大允许: %d", arguments.size(), MAX_ARGUMENT_COUNT));
        }

        for (int i = 0; i < arguments.size(); i++) {
            String arg = arguments.get(i);
            if (arg == null) {
                continue;
            }

            // 检查参数长度
            if (arg.length() > MAX_ARGUMENT_LENGTH) {
                throw new JobExecutionException(jobName, executionId, 
                        String.format("脚本参数[%d]过长: %d, 最大允许: %d", i, arg.length(), MAX_ARGUMENT_LENGTH));
            }

            // 检查命令注入
            if (containsCommandInjection(arg)) {
                auditLogger.logCommandInjectionDetection(jobName, executionId, "scriptArgument", 
                        arg, String.format("脚本参数[%d]包含命令注入字符", i));
                throw new JobExecutionException(jobName, executionId, 
                        String.format("脚本参数[%d]包含潜在的命令注入字符: %s", i, sanitizeForLog(arg)));
            }
        }

        auditLogger.logScriptSecurityValidation(jobName, executionId, null, 
                "ARGUMENT_VALIDATION", true, String.format("脚本参数安全验证通过，参数数量: %d", arguments.size()));
        log.debug("脚本参数安全验证通过: jobName={}, executionId={}, argumentCount={}", 
                jobName, executionId, arguments.size());
    }

    /**
     * 检查是否包含命令注入
     */
    private boolean containsCommandInjection(String arg) {
        return DANGEROUS_COMMAND_PATTERN.matcher(arg).find();
    }

    /**
     * 验证工作目录安全性
     */
    public void validateWorkingDirectory(String jobName, String executionId, String workingDirectory) throws JobExecutionException {
        if (!StringUtils.hasText(workingDirectory)) {
            return;
        }

        // 基本路径安全检查
        if (workingDirectory.contains("..") || workingDirectory.contains("~")) {
            throw new JobExecutionException(jobName, executionId, 
                    "工作目录包含不安全的路径模式: " + workingDirectory);
        }

        File workingDir = new File(workingDirectory);
        
        // 检查目录是否存在
        if (!workingDir.exists()) {
            throw new JobExecutionException(jobName, executionId, 
                    "工作目录不存在: " + workingDirectory);
        }

        // 检查是否为目录
        if (!workingDir.isDirectory()) {
            throw new JobExecutionException(jobName, executionId, 
                    "工作目录不是目录: " + workingDirectory);
        }

        // 检查目录权限
        if (!workingDir.canRead() || !workingDir.canExecute()) {
            throw new JobExecutionException(jobName, executionId, 
                    "工作目录权限不足: " + workingDirectory);
        }

        auditLogger.logScriptSecurityValidation(jobName, executionId, workingDir.getAbsolutePath(), 
                "WORKING_DIRECTORY_VALIDATION", true, "工作目录安全验证通过");
        log.debug("工作目录安全验证通过: jobName={}, executionId={}, workingDir={}", 
                jobName, executionId, workingDir.getAbsolutePath());
    }

    /**
     * 验证环境变量安全性
     */
    public void validateEnvironmentVariables(String jobName, String executionId, Map<String, String> envVars) throws JobExecutionException {
        if (envVars == null || envVars.isEmpty()) {
            return;
        }

        // 危险的环境变量名
        Set<String> dangerousEnvNames = new HashSet<>(Arrays.asList(
                "PATH", "LD_LIBRARY_PATH", "DYLD_LIBRARY_PATH", "JAVA_HOME", 
                "CLASSPATH", "PYTHONPATH", "NODE_PATH"
        ));

        for (Map.Entry<String, String> entry : envVars.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();

            // 检查环境变量名
            if (dangerousEnvNames.contains(name.toUpperCase())) {
                log.warn("检测到潜在危险的环境变量: jobName={}, executionId={}, envName={}", 
                        jobName, executionId, name);
            }

            // 检查环境变量值中的命令注入
            if (value != null && containsCommandInjection(value)) {
                auditLogger.logCommandInjectionDetection(jobName, executionId, "environmentVariable", 
                        value, String.format("环境变量[%s]包含命令注入字符", name));
                throw new JobExecutionException(jobName, executionId, 
                        String.format("环境变量[%s]包含潜在的命令注入字符: %s", name, sanitizeForLog(value)));
            }
        }

        auditLogger.logScriptSecurityValidation(jobName, executionId, null, 
                "ENVIRONMENT_VARIABLE_VALIDATION", true, String.format("环境变量安全验证通过，变量数量: %d", envVars.size()));
        log.debug("环境变量安全验证通过: jobName={}, executionId={}, envCount={}", 
                jobName, executionId, envVars.size());
    }

    /**
     * 为日志输出清理敏感数据
     */
    private String sanitizeForLog(String input) {
        if (input == null) {
            return null;
        }
        
        // 截断长字符串
        String result = input.length() > 100 ? input.substring(0, 100) + "..." : input;
        
        // 替换潜在敏感字符
        return result.replaceAll("[;&|`$()\\[\\]{}\\\\<>\"']", "*");
    }

    /**
     * 获取允许的脚本目录
     */
    public Set<String> getAllowedScriptDirectories() {
        return new HashSet<>(allowedScriptDirectories);
    }

    /**
     * 重新加载安全配置
     */
    public void reloadSecurityConfiguration() {
        loadSecurityConfiguration();
        log.info("脚本安全配置已重新加载，允许的脚本目录: {}", allowedScriptDirectories);
    }
}