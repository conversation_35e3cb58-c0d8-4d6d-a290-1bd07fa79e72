package com.xylink.manager.job.model;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Objects;

/**
 * Jobs配置文件的根对象
 */
public class JobsConfig {
    
    /**
     * 系统配置
     */
    @Valid
    private JobSystemConfig system = new JobSystemConfig();
    
    @NotEmpty(message = "jobs配置不能为空")
    @Valid
    private List<JobConfig> jobs;
    
    // 构造函数
    public JobsConfig() {}
    
    public JobsConfig(List<JobConfig> jobs) {
        this.jobs = jobs;
    }
    
    public JobsConfig(JobSystemConfig system, List<JobConfig> jobs) {
        this.system = system;
        this.jobs = jobs;
    }
    
    // Getter和Setter方法
    public JobSystemConfig getSystem() {
        return system;
    }
    
    public void setSystem(JobSystemConfig system) {
        this.system = system;
    }
    
    public List<JobConfig> getJobs() {
        return jobs;
    }
    
    public void setJobs(List<JobConfig> jobs) {
        this.jobs = jobs;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JobsConfig that = (JobsConfig) o;
        return Objects.equals(system, that.system) && Objects.equals(jobs, that.jobs);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(system, jobs);
    }
    
    @Override
    public String toString() {
        return "JobsConfig{" +
                "system=" + system +
                ", jobs=" + jobs +
                '}';
    }
}