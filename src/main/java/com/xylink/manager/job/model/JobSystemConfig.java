package com.xylink.manager.job.model;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * Job系统配置
 */
public class JobSystemConfig {

    /**
     * 优雅停机超时时间（秒）
     */
    @Min(value = 1, message = "优雅停机超时时间必须大于0")
    private int shutdownTimeoutSeconds = 60;

    /**
     * 执行器配置
     */
    @Valid
    @NotNull(message = "执行器配置不能为空")
    private ExecutorConfig executor = new ExecutorConfig();

    /**
     * 调度器配置
     */
    @Valid
    @NotNull(message = "调度器配置不能为空")
    private SchedulerConfig scheduler = new SchedulerConfig();

    /**
     * 分布式锁配置
     */
    @Valid
    @NotNull(message = "分布式锁配置不能为空")
    private LockConfig lock = new LockConfig();

    /**
     * 监控配置
     */
    @Valid
    @NotNull(message = "监控配置不能为空")
    private MetricsConfig metrics = new MetricsConfig();

    /**
     * 脚本安全配置
     */
    @Valid
    @NotNull(message = "脚本安全配置不能为空")
    private ScriptSecurityConfig script = new ScriptSecurityConfig();

    /**
     * Redis配置（可选）
     */
    @Valid
    private RedisConfig redis;

    // Getter和Setter方法
    public int getShutdownTimeoutSeconds() {
        return shutdownTimeoutSeconds;
    }

    public void setShutdownTimeoutSeconds(int shutdownTimeoutSeconds) {
        this.shutdownTimeoutSeconds = shutdownTimeoutSeconds;
    }

    public ExecutorConfig getExecutor() {
        return executor;
    }

    public void setExecutor(ExecutorConfig executor) {
        this.executor = executor;
    }

    public SchedulerConfig getScheduler() {
        return scheduler;
    }

    public void setScheduler(SchedulerConfig scheduler) {
        this.scheduler = scheduler;
    }

    public LockConfig getLock() {
        return lock;
    }

    public void setLock(LockConfig lock) {
        this.lock = lock;
    }

    public MetricsConfig getMetrics() {
        return metrics;
    }

    public void setMetrics(MetricsConfig metrics) {
        this.metrics = metrics;
    }

    public ScriptSecurityConfig getScript() {
        return script;
    }

    public void setScript(ScriptSecurityConfig script) {
        this.script = script;
    }

    public RedisConfig getRedis() {
        return redis;
    }

    public void setRedis(RedisConfig redis) {
        this.redis = redis;
    }

    /**
     * 执行器配置
     */
    public static class ExecutorConfig {
        @Min(value = 1, message = "核心线程数必须大于0")
        private int corePoolSize = 5;

        @Min(value = 1, message = "最大线程数必须大于0")
        private int maxPoolSize = 20;

        @Min(value = 0, message = "队列容量不能为负数")
        private int queueCapacity = 100;

        public int getCorePoolSize() {
            return corePoolSize;
        }

        public void setCorePoolSize(int corePoolSize) {
            this.corePoolSize = corePoolSize;
        }

        public int getMaxPoolSize() {
            return maxPoolSize;
        }

        public void setMaxPoolSize(int maxPoolSize) {
            this.maxPoolSize = maxPoolSize;
        }

        public int getQueueCapacity() {
            return queueCapacity;
        }

        public void setQueueCapacity(int queueCapacity) {
            this.queueCapacity = queueCapacity;
        }
    }

    /**
     * 调度器配置
     */
    public static class SchedulerConfig {
        @Min(value = 1, message = "调度器线程池大小必须大于0")
        private int poolSize = 10;

        @Min(value = 1, message = "等待终止时间必须大于0")
        private int awaitTerminationSeconds = 30;

        public int getPoolSize() {
            return poolSize;
        }

        public void setPoolSize(int poolSize) {
            this.poolSize = poolSize;
        }

        public int getAwaitTerminationSeconds() {
            return awaitTerminationSeconds;
        }

        public void setAwaitTerminationSeconds(int awaitTerminationSeconds) {
            this.awaitTerminationSeconds = awaitTerminationSeconds;
        }
    }

    /**
     * 分布式锁配置
     */
    public static class LockConfig {
        @Min(value = 1, message = "默认锁超时时间必须大于0")
        private int defaultTimeoutSeconds = 30;

        public int getDefaultTimeoutSeconds() {
            return defaultTimeoutSeconds;
        }

        public void setDefaultTimeoutSeconds(int defaultTimeoutSeconds) {
            this.defaultTimeoutSeconds = defaultTimeoutSeconds;
        }
    }

    /**
     * 监控配置
     */
    public static class MetricsConfig {
        private boolean enabled = true;

        @Min(value = 1, message = "监控推送间隔必须大于0")
        private int pushIntervalSeconds = 60;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getPushIntervalSeconds() {
            return pushIntervalSeconds;
        }

        public void setPushIntervalSeconds(int pushIntervalSeconds) {
            this.pushIntervalSeconds = pushIntervalSeconds;
        }
    }

    /**
     * 脚本安全配置
     */
    public static class ScriptSecurityConfig {
        /**
         * 允许的脚本目录列表
         */
        private List<String> allowedDirectories = Arrays.asList(
                "/opt/app/scripts",
                "/usr/local/scripts",
                "/home/<USER>"
        );

        /**
         * 是否启用严格模式（更严格的安全检查）
         */
        private boolean strictMode = true;

        /**
         * 最大脚本执行时间（秒）
         */
        @Min(value = 1, message = "最大脚本执行时间必须大于0")
        private int maxExecutionTimeSeconds = 3600;

        /**
         * 最大脚本文件大小（MB）
         */
        @Min(value = 1, message = "最大脚本文件大小必须大于0")
        private int maxFileSizeMB = 10;

        public List<String> getAllowedDirectories() {
            return allowedDirectories;
        }

        public void setAllowedDirectories(List<String> allowedDirectories) {
            this.allowedDirectories = allowedDirectories;
        }

        public boolean isStrictMode() {
            return strictMode;
        }

        public void setStrictMode(boolean strictMode) {
            this.strictMode = strictMode;
        }

        public int getMaxExecutionTimeSeconds() {
            return maxExecutionTimeSeconds;
        }

        public void setMaxExecutionTimeSeconds(int maxExecutionTimeSeconds) {
            this.maxExecutionTimeSeconds = maxExecutionTimeSeconds;
        }

        public int getMaxFileSizeMB() {
            return maxFileSizeMB;
        }

        public void setMaxFileSizeMB(int maxFileSizeMB) {
            this.maxFileSizeMB = maxFileSizeMB;
        }
    }

    /**
     * Redis配置
     */
    public static class RedisConfig {
        /**
         * 防抖动配置
         */
        @Valid
        private DebounceConfig debounce;

        public DebounceConfig getDebounce() {
            return debounce;
        }

        public void setDebounce(DebounceConfig debounce) {
            this.debounce = debounce;
        }

        /**
         * Redis防抖动配置
         */
        public static class DebounceConfig {
            /**
             * 切换到分布式模式需要的连续成功次数
             */
            @Min(value = 1, message = "成功次数必须大于0")
            private Integer requiredSuccessCount;

            /**
             * 切换到内存模式需要的连续失败次数
             */
            @Min(value = 1, message = "失败次数必须大于0")
            private Integer requiredFailureCount;

            /**
             * Redis可用性检查间隔（秒）
             */
            @Min(value = 1, message = "检查间隔必须大于0")
            private Integer checkIntervalSeconds;

            public Integer getRequiredSuccessCount() {
                return requiredSuccessCount;
            }

            public void setRequiredSuccessCount(Integer requiredSuccessCount) {
                this.requiredSuccessCount = requiredSuccessCount;
            }

            public Integer getRequiredFailureCount() {
                return requiredFailureCount;
            }

            public void setRequiredFailureCount(Integer requiredFailureCount) {
                this.requiredFailureCount = requiredFailureCount;
            }

            public Integer getCheckIntervalSeconds() {
                return checkIntervalSeconds;
            }

            public void setCheckIntervalSeconds(Integer checkIntervalSeconds) {
                this.checkIntervalSeconds = checkIntervalSeconds;
            }
        }
    }
}