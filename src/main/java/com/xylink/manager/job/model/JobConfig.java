package com.xylink.manager.job.model;

import com.xylink.manager.job.validation.ValidJobConfig;
import com.xylink.manager.job.validation.ValidCron;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * Job配置模型
 */
@ValidJobConfig
public class JobConfig {
    
    @NotBlank(message = "jobName不能为空")
    private String jobName;
    
    @NotNull(message = "type不能为空")
    private JobType type;
    
    @NotBlank(message = "cron表达式不能为空")
    @ValidCron(message = "cron表达式格式不正确")
    private String cron;
    
    private boolean enabled = true;
    
    @Min(value = 0, message = "retryCount不能小于0")
    private int retryCount = 0;
    
    @Min(value = 1, message = "retryIntervalSeconds不能小于1")
    private int retryIntervalSeconds = 60;
    
    @Min(value = 1, message = "alertThreshold不能小于1")
    private int alertThreshold = 3;
    
    private String description;
    
    @Valid
    private ApiConfig apiConfig;
    
    @Valid
    private ScriptConfig scriptConfig;
    
    // 构造函数
    public JobConfig() {}
    
    public JobConfig(String jobName, JobType type, String cron) {
        this.jobName = jobName;
        this.type = type;
        this.cron = cron;
    }
    
    // Getter和Setter方法
    public String getJobName() {
        return jobName;
    }
    
    public void setJobName(String jobName) {
        this.jobName = jobName;
    }
    
    public JobType getType() {
        return type;
    }
    
    public void setType(JobType type) {
        this.type = type;
    }
    
    public String getCron() {
        return cron;
    }
    
    public void setCron(String cron) {
        this.cron = cron;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public int getRetryCount() {
        return retryCount;
    }
    
    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }
    
    public int getRetryIntervalSeconds() {
        return retryIntervalSeconds;
    }
    
    public void setRetryIntervalSeconds(int retryIntervalSeconds) {
        this.retryIntervalSeconds = retryIntervalSeconds;
    }
    
    public int getAlertThreshold() {
        return alertThreshold;
    }
    
    public void setAlertThreshold(int alertThreshold) {
        this.alertThreshold = alertThreshold;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public ApiConfig getApiConfig() {
        return apiConfig;
    }
    
    public void setApiConfig(ApiConfig apiConfig) {
        this.apiConfig = apiConfig;
    }
    
    public ScriptConfig getScriptConfig() {
        return scriptConfig;
    }
    
    public void setScriptConfig(ScriptConfig scriptConfig) {
        this.scriptConfig = scriptConfig;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JobConfig jobConfig = (JobConfig) o;
        return Objects.equals(jobName, jobConfig.jobName);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(jobName);
    }
    
    @Override
    public String toString() {
        return "JobConfig{" +
                "jobName='" + jobName + '\'' +
                ", type=" + type +
                ", cron='" + cron + '\'' +
                ", enabled=" + enabled +
                ", retryCount=" + retryCount +
                ", retryIntervalSeconds=" + retryIntervalSeconds +
                ", alertThreshold=" + alertThreshold +
                ", description='" + description + '\'' +
                '}';
    }
}