package com.xylink.manager.job.model;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Job执行上下文
 */
public class JobContext {
    
    private String executionId;
    private JobConfig jobConfig;
    private LocalDateTime triggerTime;
    private int currentRetryCount;
    private String executionNode;
    private String jobName; // 用于在没有jobConfig时存储jobName
    
    // 构造函数
    public JobContext() {}
    
    public JobContext(String executionId, JobConfig jobConfig) {
        this.executionId = executionId;
        this.jobConfig = jobConfig;
        this.triggerTime = LocalDateTime.now();
        this.currentRetryCount = 0;
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.currentRetryCount++;
    }
    
    /**
     * 判断是否还可以重试
     */
    public boolean canRetry() {
        return this.currentRetryCount < this.jobConfig.getRetryCount();
    }
    
    /**
     * 获取Job名称
     */
    public String getJobName() {
        if (jobConfig != null) {
            return jobConfig.getJobName();
        }
        return jobName;
    }
    
    /**
     * 设置Job名称（用于没有jobConfig的情况）
     */
    public void setJobName(String jobName) {
        this.jobName = jobName;
    }
    
    /**
     * 获取Job类型
     */
    public JobType getJobType() {
        return jobConfig != null ? jobConfig.getType() : null;
    }
    
    // Getter和Setter方法
    public String getExecutionId() {
        return executionId;
    }
    
    public void setExecutionId(String executionId) {
        this.executionId = executionId;
    }
    
    public JobConfig getJobConfig() {
        return jobConfig;
    }
    
    public void setJobConfig(JobConfig jobConfig) {
        this.jobConfig = jobConfig;
    }
    
    public LocalDateTime getTriggerTime() {
        return triggerTime;
    }
    
    public void setTriggerTime(LocalDateTime triggerTime) {
        this.triggerTime = triggerTime;
    }
    
    public int getCurrentRetryCount() {
        return currentRetryCount;
    }
    
    public void setCurrentRetryCount(int currentRetryCount) {
        this.currentRetryCount = currentRetryCount;
    }
    
    public String getExecutionNode() {
        return executionNode;
    }
    
    public void setExecutionNode(String executionNode) {
        this.executionNode = executionNode;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JobContext that = (JobContext) o;
        return Objects.equals(executionId, that.executionId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(executionId);
    }
    
    @Override
    public String toString() {
        return "JobContext{" +
                "executionId='" + executionId + '\'' +
                ", jobName='" + getJobName() + '\'' +
                ", jobType=" + getJobType() +
                ", triggerTime=" + triggerTime +
                ", currentRetryCount=" + currentRetryCount +
                ", executionNode='" + executionNode + '\'' +
                '}';
    }
}