package com.xylink.manager.job.model;

import lombok.Data;

/**
 * Job执行结果
 */
@Data
public class JobExecutionResult {
    
    /**
     * 执行状态
     */
    private JobExecutionStatus status;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long durationMs;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 执行详情（如HTTP响应、脚本输出等）
     */
    private String details;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 创建成功结果
     */
    public static JobExecutionResult success(Long durationMs, String details) {
        JobExecutionResult result = new JobExecutionResult();
        result.setStatus(JobExecutionStatus.SUCCESS);
        result.setDurationMs(durationMs);
        result.setDetails(details);
        result.setRetryCount(0);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static JobExecutionResult failure(Long durationMs, String errorMessage, String details, Integer retryCount) {
        JobExecutionResult result = new JobExecutionResult();
        result.setStatus(JobExecutionStatus.FAILURE);
        result.setDurationMs(durationMs);
        result.setErrorMessage(errorMessage);
        result.setDetails(details);
        result.setRetryCount(retryCount);
        return result;
    }
    
    /**
     * 创建超时结果
     */
    public static JobExecutionResult timeout(Long durationMs, String details, Integer retryCount) {
        JobExecutionResult result = new JobExecutionResult();
        result.setStatus(JobExecutionStatus.TIMEOUT);
        result.setDurationMs(durationMs);
        result.setErrorMessage("Job execution timeout");
        result.setDetails(details);
        result.setRetryCount(retryCount);
        return result;
    }
    
    /**
     * 判断执行是否成功
     */
    public boolean isSuccess() {
        return JobExecutionStatus.SUCCESS.equals(this.status);
    }
    
    /**
     * 判断执行是否失败
     */
    public boolean isFailure() {
        return JobExecutionStatus.FAILURE.equals(this.status) || JobExecutionStatus.TIMEOUT.equals(this.status);
    }
}