package com.xylink.manager.job.model;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Map;
import java.util.Objects;

/**
 * API类型Job的配置
 */
public class ApiConfig {
    
    @NotBlank(message = "targetUrl不能为空")
    @Pattern(regexp = "^https?://[\\w\\-]+(\\.[\\w\\-]+)*([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?$", 
             message = "targetUrl必须是有效的URL格式")
    private String targetUrl;
    
    @NotBlank(message = "httpMethod不能为空")
    @Pattern(regexp = "^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)$", 
             message = "httpMethod必须是有效的HTTP方法")
    private String httpMethod = "GET";
    
    @Min(value = 1, message = "requestTimeoutSeconds不能小于1")
    private int requestTimeoutSeconds = 30;
    
    private Map<String, String> headers;
    
    private String body;
    
    // 构造函数
    public ApiConfig() {}
    
    public ApiConfig(String targetUrl, String httpMethod) {
        this.targetUrl = targetUrl;
        this.httpMethod = httpMethod;
    }
    
    // Getter和Setter方法
    public String getTargetUrl() {
        return targetUrl;
    }
    
    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }
    
    public String getHttpMethod() {
        return httpMethod;
    }
    
    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }
    
    public int getRequestTimeoutSeconds() {
        return requestTimeoutSeconds;
    }
    
    public void setRequestTimeoutSeconds(int requestTimeoutSeconds) {
        this.requestTimeoutSeconds = requestTimeoutSeconds;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public String getBody() {
        return body;
    }
    
    public void setBody(String body) {
        this.body = body;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ApiConfig apiConfig = (ApiConfig) o;
        return requestTimeoutSeconds == apiConfig.requestTimeoutSeconds &&
                Objects.equals(targetUrl, apiConfig.targetUrl) &&
                Objects.equals(httpMethod, apiConfig.httpMethod) &&
                Objects.equals(headers, apiConfig.headers) &&
                Objects.equals(body, apiConfig.body);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(targetUrl, httpMethod, requestTimeoutSeconds, headers, body);
    }
    
    @Override
    public String toString() {
        return "ApiConfig{" +
                "targetUrl='" + targetUrl + '\'' +
                ", httpMethod='" + httpMethod + '\'' +
                ", requestTimeoutSeconds=" + requestTimeoutSeconds +
                ", headers=" + headers +
                ", body='" + body + '\'' +
                '}';
    }
}