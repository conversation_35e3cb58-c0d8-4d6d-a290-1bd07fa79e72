package com.xylink.manager.job.model;

import lombok.Data;

/**
 * Job执行历史记录
 */
@Data
public class JobExecutionHistory {
    
    /**
     * 任务名称
     */
    private String jobName;
    
    /**
     * 执行ID
     */
    private String executionId;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 完成时间
     */
    private String finishTime;
    
    /**
     * 执行状态（SUCCESS/FAIL）
     */
    private String status;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long durationMs;
    
    /**
     * 执行节点
     */
    private String executionNode;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
}