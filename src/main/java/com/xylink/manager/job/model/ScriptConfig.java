package com.xylink.manager.job.model;

import com.xylink.manager.job.validation.ValidScriptPath;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;

/**
 * 脚本类型Job的配置
 */
public class ScriptConfig {
    
    @NotBlank(message = "path不能为空")
    @ValidScriptPath(message = "脚本路径不安全或格式不正确")
    private String path;
    
    private String workingDirectory;
    
    private List<String> arguments;
    
    private java.util.Map<String, String> environmentVariables;
    
    @Min(value = 1, message = "timeoutSeconds不能小于1")
    private Integer timeoutSeconds = 300; // 默认5分钟超时
    
    // 构造函数
    public ScriptConfig() {}
    
    public ScriptConfig(String path) {
        this.path = path;
    }
    
    // Getter和Setter方法
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getWorkingDirectory() {
        return workingDirectory;
    }
    
    public void setWorkingDirectory(String workingDirectory) {
        this.workingDirectory = workingDirectory;
    }
    
    public List<String> getArguments() {
        return arguments;
    }
    
    public void setArguments(List<String> arguments) {
        this.arguments = arguments;
    }
    
    public java.util.Map<String, String> getEnvironmentVariables() {
        return environmentVariables;
    }
    
    public void setEnvironmentVariables(java.util.Map<String, String> environmentVariables) {
        this.environmentVariables = environmentVariables;
    }
    
    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ScriptConfig that = (ScriptConfig) o;
        return Objects.equals(path, that.path) &&
                Objects.equals(workingDirectory, that.workingDirectory) &&
                Objects.equals(arguments, that.arguments) &&
                Objects.equals(environmentVariables, that.environmentVariables) &&
                Objects.equals(timeoutSeconds, that.timeoutSeconds);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(path, workingDirectory, arguments, environmentVariables, timeoutSeconds);
    }
    
    @Override
    public String toString() {
        return "ScriptConfig{" +
                "path='" + path + '\'' +
                ", workingDirectory='" + workingDirectory + '\'' +
                ", arguments=" + arguments +
                ", environmentVariables=" + environmentVariables +
                ", timeoutSeconds=" + timeoutSeconds +
                '}';
    }
}