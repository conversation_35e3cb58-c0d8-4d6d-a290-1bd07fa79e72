package com.xylink.manager.job.model;

import lombok.Data;

/**
 * Job状态信息
 */
@Data
public class JobStatus {
    
    /**
     * 任务名称
     */
    private String jobName;
    
    /**
     * 任务类型
     */
    private JobType type;
    
    /**
     * 是否启用
     */
    private boolean enabled;
    
    /**
     * Cron表达式
     */
    private String cron;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 最近触发时间
     */
    private String lastTriggerTime;
    
    /**
     * 最近执行节点
     */
    private String lastExecutionNode;
    
    /**
     * 最近执行ID
     */
    private String lastExecutionId;
    
    /**
     * 最近执行状态（SUCCESS/FAIL）
     */
    private String lastStatus;
    
    /**
     * 最近完成时间
     */
    private String lastFinishTime;
    
    /**
     * 最近执行耗时（毫秒）
     */
    private Long lastDurationMs;
    
    /**
     * 最近一次成功时间
     */
    private String lastSuccessTime;
    
    /**
     * 连续失败次数
     */
    private Integer consecutiveFailCount;
    
    /**
     * 是否正在运行
     */
    private boolean isRunning;
}