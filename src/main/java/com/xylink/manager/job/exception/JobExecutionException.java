package com.xylink.manager.job.exception;

/**
 * Job执行异常
 */
public class JobExecutionException extends JobException {
    
    private static final long serialVersionUID = 1L;
    
    private final String jobName;
    private final String executionId;
    
    public JobExecutionException(String jobName, String executionId, String message) {
        super(String.format("Job执行失败 [jobName=%s, executionId=%s]: %s", jobName, executionId, message));
        this.jobName = jobName;
        this.executionId = executionId;
    }
    
    public JobExecutionException(String jobName, String executionId, String message, Throwable cause) {
        super(String.format("Job执行失败 [jobName=%s, executionId=%s]: %s", jobName, executionId, message), cause);
        this.jobName = jobName;
        this.executionId = executionId;
    }
    
    public JobExecutionException(String jobName, String executionId, Throwable cause) {
        super(String.format("Job执行失败 [jobName=%s, executionId=%s]", jobName, executionId), cause);
        this.jobName = jobName;
        this.executionId = executionId;
    }
    
    public String getJobName() {
        return jobName;
    }
    
    public String getExecutionId() {
        return executionId;
    }
}