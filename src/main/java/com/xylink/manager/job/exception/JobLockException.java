package com.xylink.manager.job.exception;

/**
 * Job锁相关异常
 */
public class JobLockException extends JobException {
    
    private static final long serialVersionUID = 1L;
    
    private final String jobName;
    
    public JobLockException(String jobName, String message) {
        super(String.format("Job锁操作失败 [jobName=%s]: %s", jobName, message));
        this.jobName = jobName;
    }
    
    public JobLockException(String jobName, String message, Throwable cause) {
        super(String.format("Job锁操作失败 [jobName=%s]: %s", jobName, message), cause);
        this.jobName = jobName;
    }
    
    public String getJobName() {
        return jobName;
    }
}