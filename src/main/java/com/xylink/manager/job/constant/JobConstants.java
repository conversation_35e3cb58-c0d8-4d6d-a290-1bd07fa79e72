package com.xylink.manager.job.constant;

/**
 * Job相关常量
 */
public class JobConstants {
    
    /**
     * 配置文件名
     */
    public static final String CONFIG_FILE_NAME = "jobs.yaml";
    
    /**
     * 默认配置文件路径
     */
    public static final String DEFAULT_CONFIG_PATH = "classpath:" + CONFIG_FILE_NAME;
    
    /**
     * 外部配置文件路径
     */
    public static final String EXTERNAL_CONFIG_PATH = "file:./" + CONFIG_FILE_NAME;
    
    /**
     * Redis key前缀
     */
    public static final String REDIS_KEY_PREFIX = "job_";
    
    /**
     * 分布式锁key前缀
     */
    public static final String LOCK_KEY_PREFIX = REDIS_KEY_PREFIX + "lock:";
    
    /**
     * Job状态key前缀
     */
    public static final String STATUS_KEY_PREFIX = REDIS_KEY_PREFIX + "status:";
    
    /**
     * 正在运行Job key前缀
     */
    public static final String RUNNING_KEY_PREFIX = REDIS_KEY_PREFIX + "running:";
    
    /**
     * Job执行历史key前缀
     */
    public static final String HISTORY_KEY_PREFIX = REDIS_KEY_PREFIX + "history:";
    
    /**
     * 默认锁超时时间（秒）
     */
    public static final int DEFAULT_LOCK_TIMEOUT_SECONDS = 30;
    
    /**
     * 默认请求超时时间（秒）
     */
    public static final int DEFAULT_REQUEST_TIMEOUT_SECONDS = 30;
    
    /**
     * 默认脚本超时时间（秒）
     */
    public static final int DEFAULT_SCRIPT_TIMEOUT_SECONDS = 300;
    
    /**
     * 默认重试间隔（秒）
     */
    public static final int DEFAULT_RETRY_INTERVAL_SECONDS = 60;
    
    /**
     * 默认告警阈值
     */
    public static final int DEFAULT_ALERT_THRESHOLD = 3;
    
    /**
     * 最大历史记录数量
     */
    public static final int MAX_HISTORY_SIZE = 100;
    
    /**
     * MDC中的执行ID key
     */
    public static final String MDC_EXECUTION_ID = "executionId";
    
    /**
     * MDC中的Job名称 key
     */
    public static final String MDC_JOB_NAME = "jobName";
    
    /**
     * 日志记录器名称
     */
    public static final String JOB_LOGGER_NAME = "com.xylink.manager.job";
    
    /**
     * HTTP方法
     */
    public static class HttpMethod {
        public static final String GET = "GET";
        public static final String POST = "POST";
        public static final String PUT = "PUT";
        public static final String DELETE = "DELETE";
        public static final String PATCH = "PATCH";
    }
    
    /**
     * 敏感字段名称（用于日志脱敏）
     */
    public static class SensitiveFields {
        public static final String[] FIELD_NAMES = {
            "password", "pwd", "token", "apiKey", "api_key", "secret", 
            "authorization", "auth", "idCard", "id_card", "phone", 
            "mobile", "email", "creditCard", "credit_card"
        };
    }
    
    private JobConstants() {
        // 工具类，禁止实例化
    }
}