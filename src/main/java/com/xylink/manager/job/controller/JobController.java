package com.xylink.manager.job.controller;

import com.xylink.manager.job.config.RedisDebounceConfig;
import com.xylink.manager.job.exception.JobConfigException;
import com.xylink.manager.job.lock.JobDistributedLock;
import com.xylink.manager.job.metrics.JobMetrics;
import com.xylink.manager.job.model.*;
import com.xylink.manager.job.scheduler.JobScheduler;
import com.xylink.manager.job.security.JobSecurityAuditLogger;
import com.xylink.manager.job.service.JobStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Job管理REST API控制器
 * 提供Job配置查询、状态查询、历史查询和配置重载等功能
 */
@Slf4j
@RestController
@RequestMapping("/jobs")
@ConditionalOnProperty(name = "job.module.enabled", havingValue = "true", matchIfMissing = false)
public class JobController {

    @Autowired
    private JobScheduler jobScheduler;

    @Autowired
    private JobStatusService jobStatusService;

    @Autowired
    private JobDistributedLock jobDistributedLock;

    @Autowired
    private JobMetrics jobMetrics;

    @Autowired
    private JobSecurityAuditLogger auditLogger;

    @Autowired
    private RedisDebounceConfig redisDebounceConfig;

    /**
     * 获取所有生效的Job配置
     * GET /jobs/configs
     */
    @GetMapping("/configs")
    public ResponseEntity<Map<String, Object>> getAllJobConfigs() {
        try {
            List<JobConfig> configs = jobScheduler.getAllJobConfigs();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", configs);
            response.put("total", configs.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Job配置失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取Job配置失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有Job状态
     * GET /jobs/status
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getAllJobStatuses() {
        try {
            List<JobStatus> statuses = jobStatusService.getAllJobStatuses();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statuses);
            response.put("total", statuses.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Job状态失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取Job状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取单个Job状态
     * GET /jobs/{jobName}/status
     */
    @GetMapping("/{jobName}/status")
    public ResponseEntity<Map<String, Object>> getJobStatus(@PathVariable String jobName) {
        try {
            JobStatus status = jobStatusService.getJobStatus(jobName);
            
            if (status == null) {
                return ResponseEntity.notFound().build();
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", status);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Job状态失败: jobName={}", jobName, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取Job状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取正在运行的Job
     * GET /jobs/running
     */
    @GetMapping("/running")
    public ResponseEntity<Map<String, Object>> getRunningJobs() {
        try {
            List<RunningJobInfo> runningJobs = jobStatusService.getRunningJobs();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", runningJobs);
            response.put("total", runningJobs.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取正在运行的Job失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取正在运行的Job失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有Job执行历史
     * GET /jobs/history?page=0&size=20
     */
    @GetMapping("/history")
    public ResponseEntity<Map<String, Object>> getAllJobHistory(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<JobExecutionHistory> history = jobStatusService.getAllJobHistory(page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", history);
            response.put("page", page);
            response.put("size", size);
            response.put("total", history.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Job执行历史失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取Job执行历史失败: " + e.getMessage()));
        }
    }

    /**
     * 获取单个Job执行历史
     * GET /jobs/{jobName}/history?page=0&size=20
     */
    @GetMapping("/{jobName}/history")
    public ResponseEntity<Map<String, Object>> getJobHistory(
            @PathVariable String jobName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<JobExecutionHistory> history = jobStatusService.getJobHistory(jobName, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", history);
            response.put("jobName", jobName);
            response.put("page", page);
            response.put("size", size);
            response.put("total", history.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Job执行历史失败: jobName={}", jobName, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取Job执行历史失败: " + e.getMessage()));
        }
    }

    /**
     * 重新加载Job配置
     * POST /jobs/reload
     */
    @PostMapping("/reload")
    public ResponseEntity<Map<String, Object>> reloadJobs(HttpServletRequest request) {
        String operator = getOperator(request);
        String clientIp = getClientIp(request);
        
        try {
            int oldScheduledCount = jobScheduler.getScheduledJobCount();
            jobScheduler.reloadJobs();
            int newScheduledCount = jobScheduler.getScheduledJobCount();
            
            Map<String, Object> operationDetails = new HashMap<>();
            operationDetails.put("oldScheduledCount", oldScheduledCount);
            operationDetails.put("newScheduledCount", newScheduledCount);
            operationDetails.put("clientIp", clientIp);
            
            auditLogger.logJobManagementOperation("RELOAD_JOBS", null, operator, 
                    operationDetails, true, "Job配置重新加载成功");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Job配置重新加载成功");
            response.put("scheduledJobCount", newScheduledCount);
            
            log.info("Job配置重新加载成功");
            return ResponseEntity.ok(response);
        } catch (JobConfigException e) {
            auditLogger.logJobManagementOperation("RELOAD_JOBS", null, operator, 
                    singletonMap("clientIp", clientIp), false, "重新加载Job配置失败: " + e.getMessage());
            log.error("重新加载Job配置失败", e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse("重新加载Job配置失败: " + e.getMessage()));
        } catch (Exception e) {
            auditLogger.logJobManagementOperation("RELOAD_JOBS", null, operator, 
                    singletonMap("clientIp", clientIp), false, "重新加载Job配置失败: " + e.getMessage());
            log.error("重新加载Job配置失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("重新加载Job配置失败: " + e.getMessage()));
        }
    }

    /**
     * 手动触发Job执行
     * POST /jobs/{jobName}/trigger
     */
    @PostMapping("/{jobName}/trigger")
    public ResponseEntity<Map<String, Object>> triggerJob(@PathVariable String jobName, HttpServletRequest request) {
        String operator = getOperator(request);
        String clientIp = getClientIp(request);
        
        try {
            jobScheduler.triggerJob(jobName);
            
            Map<String, Object> operationDetails = new HashMap<>();
            operationDetails.put("clientIp", clientIp);
            operationDetails.put("triggerType", "MANUAL");
            
            auditLogger.logJobManagementOperation("TRIGGER_JOB", jobName, operator, 
                    operationDetails, true, "Job手动触发成功");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Job触发成功");
            response.put("jobName", jobName);
            
            log.info("手动触发Job成功: jobName={}", jobName);
            return ResponseEntity.ok(response);
        } catch (JobConfigException e) {
            auditLogger.logJobManagementOperation("TRIGGER_JOB", jobName, operator, 
                    singletonMap("clientIp", clientIp), false, "手动触发Job失败: " + e.getMessage());
            log.error("手动触发Job失败: jobName={}", jobName, e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse("手动触发Job失败: " + e.getMessage()));
        } catch (Exception e) {
            auditLogger.logJobManagementOperation("TRIGGER_JOB", jobName, operator, 
                    singletonMap("clientIp", clientIp), false, "手动触发Job失败: " + e.getMessage());
            log.error("手动触发Job失败: jobName={}", jobName, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("手动触发Job失败: " + e.getMessage()));
        }
    }

    /**
     * 启用Job
     * POST /jobs/{jobName}/enable
     */
    @PostMapping("/{jobName}/enable")
    public ResponseEntity<Map<String, Object>> enableJob(@PathVariable String jobName, HttpServletRequest request) {
        String operator = getOperator(request);
        String clientIp = getClientIp(request);
        
        try {
            jobScheduler.enableJob(jobName);
            
            Map<String, Object> operationDetails = new HashMap<>();
            operationDetails.put("clientIp", clientIp);
            operationDetails.put("previousState", "DISABLED");
            operationDetails.put("newState", "ENABLED");
            
            auditLogger.logJobManagementOperation("ENABLE_JOB", jobName, operator, 
                    operationDetails, true, "Job启用成功");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Job启用成功");
            response.put("jobName", jobName);
            
            log.info("启用Job成功: jobName={}", jobName);
            return ResponseEntity.ok(response);
        } catch (JobConfigException e) {
            auditLogger.logJobManagementOperation("ENABLE_JOB", jobName, operator, 
                    singletonMap("clientIp", clientIp), false, "启用Job失败: " + e.getMessage());
            log.error("启用Job失败: jobName={}", jobName, e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse("启用Job失败: " + e.getMessage()));
        } catch (Exception e) {
            auditLogger.logJobManagementOperation("ENABLE_JOB", jobName, operator, 
                    singletonMap("clientIp", clientIp), false, "启用Job失败: " + e.getMessage());
            log.error("启用Job失败: jobName={}", jobName, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("启用Job失败: " + e.getMessage()));
        }
    }

    /**
     * 禁用Job
     * POST /jobs/{jobName}/disable
     */
    @PostMapping("/{jobName}/disable")
    public ResponseEntity<Map<String, Object>> disableJob(@PathVariable String jobName, HttpServletRequest request) {
        String operator = getOperator(request);
        String clientIp = getClientIp(request);
        
        try {
            jobScheduler.disableJob(jobName);
            
            Map<String, Object> operationDetails = new HashMap<>();
            operationDetails.put("clientIp", clientIp);
            operationDetails.put("previousState", "ENABLED");
            operationDetails.put("newState", "DISABLED");
            
            auditLogger.logJobManagementOperation("DISABLE_JOB", jobName, operator, 
                    operationDetails, true, "Job禁用成功");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Job禁用成功");
            response.put("jobName", jobName);
            
            log.info("禁用Job成功: jobName={}", jobName);
            return ResponseEntity.ok(response);
        } catch (JobConfigException e) {
            auditLogger.logJobManagementOperation("DISABLE_JOB", jobName, operator, 
                    singletonMap("clientIp", clientIp), false, "禁用Job失败: " + e.getMessage());
            log.error("禁用Job失败: jobName={}", jobName, e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse("禁用Job失败: " + e.getMessage()));
        } catch (Exception e) {
            auditLogger.logJobManagementOperation("DISABLE_JOB", jobName, operator, 
                    singletonMap("clientIp", clientIp), false, "禁用Job失败: " + e.getMessage());
            log.error("禁用Job失败: jobName={}", jobName, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("禁用Job失败: " + e.getMessage()));
        }
    }

    /**
     * 强制释放Job锁（紧急操作）
     * DELETE /jobs/{jobName}/lock
     */
    @DeleteMapping("/{jobName}/lock")
    public ResponseEntity<Map<String, Object>> forceReleaseLock(@PathVariable String jobName, HttpServletRequest request) {
        String operator = getOperator(request);
        String clientIp = getClientIp(request);
        
        try {
            // 检查锁是否存在
            boolean isLocked = jobDistributedLock.isLocked(jobName);
            
            Map<String, Object> operationDetails = new HashMap<>();
            operationDetails.put("clientIp", clientIp);
            operationDetails.put("lockExisted", isLocked);
            
            if (!isLocked) {
                auditLogger.logJobManagementOperation("FORCE_RELEASE_LOCK", jobName, operator, 
                        operationDetails, true, "Job锁不存在，无需释放");
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "Job锁不存在，无需释放");
                response.put("jobName", jobName);
                return ResponseEntity.ok(response);
            }
            
            // 强制释放锁
            jobDistributedLock.forceReleaseLock(jobName);
            
            auditLogger.logJobManagementOperation("FORCE_RELEASE_LOCK", jobName, operator, 
                    operationDetails, true, "Job锁强制释放成功");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Job锁强制释放成功");
            response.put("jobName", jobName);
            
            log.warn("强制释放Job锁: jobName={}", jobName);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            auditLogger.logJobManagementOperation("FORCE_RELEASE_LOCK", jobName, operator, 
                    singletonMap("clientIp", clientIp), false, "强制释放Job锁失败: " + e.getMessage());
            log.error("强制释放Job锁失败: jobName={}", jobName, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("强制释放Job锁失败: " + e.getMessage()));
        }
    }

    /**
     * 获取Job锁状态
     * GET /jobs/{jobName}/lock
     */
    @GetMapping("/{jobName}/lock")
    public ResponseEntity<Map<String, Object>> getLockStatus(@PathVariable String jobName) {
        try {
            boolean isLocked = jobDistributedLock.isLocked(jobName);
            boolean isHeldByCurrentThread = jobDistributedLock.isHeldByCurrentThread(jobName);
            
            Map<String, Object> lockInfo = new HashMap<>();
            lockInfo.put("jobName", jobName);
            lockInfo.put("isLocked", isLocked);
            lockInfo.put("isHeldByCurrentThread", isHeldByCurrentThread);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", lockInfo);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Job锁状态失败: jobName={}", jobName, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取Job锁状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取调度器状态
     * GET /jobs/scheduler/status
     */
    @GetMapping("/scheduler/status")
    public ResponseEntity<Map<String, Object>> getSchedulerStatus() {
        try {
            Map<String, Object> schedulerInfo = new HashMap<>();
            schedulerInfo.put("initialized", jobScheduler.isInitialized());
            schedulerInfo.put("shutdown", jobScheduler.isShutdown());
            schedulerInfo.put("scheduledJobCount", jobScheduler.getScheduledJobCount());
            schedulerInfo.put("status", jobScheduler.getSchedulerStatus());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", schedulerInfo);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取调度器状态失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取调度器状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取Job监控指标
     * GET /jobs/metrics
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getJobMetrics() {
        try {
            Map<String, Object> metrics = jobMetrics.getMetricsSummary();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", metrics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Job监控指标失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取Job监控指标失败: " + e.getMessage()));
        }
    }

    /**
     * 重置Job监控指标
     * POST /jobs/metrics/reset
     */
    @PostMapping("/metrics/reset")
    public ResponseEntity<Map<String, Object>> resetJobMetrics() {
        try {
            jobMetrics.resetMetrics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Job监控指标重置成功");
            
            log.info("Job监控指标重置成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("重置Job监控指标失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("重置Job监控指标失败: " + e.getMessage()));
        }
    }

    /**
     * 手动推送指标到N9E
     * POST /jobs/metrics/push
     */
    @PostMapping("/metrics/push")
    public ResponseEntity<Map<String, Object>> pushMetricsToN9E() {
        try {
            jobMetrics.pushMetricsToN9E();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Job指标推送到N9E成功");
            response.put("n9eAvailable", jobMetrics.isN9eAvailable());
            
            log.info("手动推送Job指标到N9E成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("推送Job指标到N9E失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("推送Job指标到N9E失败: " + e.getMessage()));
        }
    }

    /**
     * 查询N9E中的Job指标
     * GET /jobs/metrics/n9e?metric=job_execution_total&jobName=test-job
     */
    @GetMapping("/metrics/n9e")
    public ResponseEntity<Map<String, Object>> queryN9eJobMetrics(
            @RequestParam String metric,
            @RequestParam(required = false) String jobName) {
        try {
            if (!jobMetrics.isN9eAvailable()) {
                return ResponseEntity.badRequest()
                        .body(createErrorResponse("N9E监控系统不可用"));
            }
            
            Object result = jobMetrics.queryJobMetrics(metric, jobName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            response.put("metric", metric);
            response.put("jobName", jobName);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("查询N9E Job指标失败: metric={}, jobName={}", metric, jobName, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("查询N9E Job指标失败: " + e.getMessage()));
        }
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        return response;
    }

    /**
     * 获取操作者信息（从请求中提取）
     */
    private String getOperator(HttpServletRequest request) {
        // 尝试从请求头中获取用户信息
        String operator = request.getHeader("X-User-Name");
        if (operator == null || operator.trim().isEmpty()) {
            operator = request.getHeader("X-User-Id");
        }
        if (operator == null || operator.trim().isEmpty()) {
            operator = request.getRemoteAddr(); // 使用IP作为fallback
        }
        return operator != null ? operator : "unknown-operator";
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String clientIp = request.getHeader("X-Forwarded-For");
        if (clientIp == null || clientIp.trim().isEmpty()) {
            clientIp = request.getHeader("X-Real-IP");
        }
        if (clientIp == null || clientIp.trim().isEmpty()) {
            clientIp = request.getRemoteAddr();
        }
        return clientIp;
    }
    
    /**
     * 检查Redis恢复状态
     * POST /jobs/redis/check
     */
    @PostMapping("/redis/check")
    public ResponseEntity<Map<String, Object>> checkRedisRecovery(HttpServletRequest request) {
        String operator = getOperator(request);
        String clientIp = getClientIp(request);

        try {
            // 检查分布式锁的Redis状态
            boolean lockRedisAvailable = jobDistributedLock.checkAndRecoverRedis();

            // 检查状态服务的Redis状态
            boolean statusRedisAvailable = jobStatusService.checkAndRecoverRedis();

            // 获取当前模式
            String lockMode = jobDistributedLock.getLockMode();
            String storageMode = jobStatusService.getStorageMode();

            Map<String, Object> operationDetails = new HashMap<>();
            operationDetails.put("clientIp", clientIp);
            operationDetails.put("lockRedisAvailable", lockRedisAvailable);
            operationDetails.put("statusRedisAvailable", statusRedisAvailable);
            operationDetails.put("lockMode", lockMode);
            operationDetails.put("storageMode", storageMode);

            boolean overallSuccess = lockRedisAvailable && statusRedisAvailable;
            String message = overallSuccess ? "Redis连接检查成功，已切换回分布式模式" :
                           "Redis连接检查完成，部分服务仍使用内存模式";

            auditLogger.logJobManagementOperation("CHECK_REDIS", "SYSTEM", operator,
                    operationDetails, overallSuccess, message);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", message);
            response.put("lockRedisAvailable", lockRedisAvailable);
            response.put("statusRedisAvailable", statusRedisAvailable);
            response.put("lockMode", lockMode);
            response.put("storageMode", storageMode);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("检查Redis恢复状态失败", e);

            Map<String, Object> operationDetails = new HashMap<>();
            operationDetails.put("clientIp", clientIp);
            operationDetails.put("error", e.getMessage());

            auditLogger.logJobManagementOperation("CHECK_REDIS", "SYSTEM", operator,
                    operationDetails, false, "Redis恢复检查失败: " + e.getMessage());

            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("检查Redis恢复状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取系统模式状态
     * GET /jobs/system/mode
     */
    @GetMapping("/system/mode")
    public ResponseEntity<Map<String, Object>> getSystemMode() {
        try {
            String lockMode = jobDistributedLock.getLockMode();
            String storageMode = jobStatusService.getStorageMode();

            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("lockMode", lockMode);
            systemInfo.put("storageMode", storageMode);
            systemInfo.put("isDistributedMode", lockMode.contains("DISTRIBUTED") && storageMode.contains("REDIS"));
            systemInfo.put("isMemoryMode", lockMode.contains("MEMORY") || storageMode.contains("MEMORY"));

            // 添加防抖动状态信息
            systemInfo.put("debounceConfig", redisDebounceConfig.getDebounceConfig());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", systemInfo);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取系统模式状态失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取系统模式状态失败: " + e.getMessage()));
        }
    }

    /**
     * 创建单键值对Map的辅助方法（替代Map.of）
     */
    private Map<String, Object> singletonMap(String key, String value) {
        Map<String, Object> map = new HashMap<>();
        map.put(key, value);
        return map;
    }
}