package com.xylink.manager.job.metrics;

import com.xylink.manager.job.executor.JobExecutor;
import com.xylink.manager.job.model.JobType;
import com.xylink.manager.job.model.RunningJobInfo;
import com.xylink.manager.job.service.JobStatusService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Job活跃数量指标更新器
 * 定期更新当前活跃Job数量指标
 */
@Component
@Slf4j
public class JobActiveCountUpdater {
    
    @Autowired
    private JobMetrics jobMetrics;
    
    @Autowired
    private JobExecutor jobExecutor;
    
    @Autowired
    private JobStatusService jobStatusService;
    
    /**
     * 每30秒更新一次活跃Job数量指标
     */
    @Scheduled(fixedRate = 30000) // 30秒
    public void updateActiveJobCount() {
        try {
            // 获取当前正在运行的Job总数
            int totalRunningJobs = jobExecutor.getRunningJobCount();
            
            // 获取正在运行的Job详情
            List<RunningJobInfo> runningJobs = jobStatusService.getRunningJobs();
            
            // 按类型统计活跃Job数量
            int apiJobCount = 0;
            int scriptJobCount = 0;
            
            for (RunningJobInfo runningJob : runningJobs) {
                // 这里需要根据jobName获取Job类型，简化处理
                // 实际实现中可以在RunningJobInfo中包含type信息
                String jobName = runningJob.getJobName();
                if (jobName.toLowerCase().contains("api")) {
                    apiJobCount++;
                } else if (jobName.toLowerCase().contains("script")) {
                    scriptJobCount++;
                }
            }
            
            // 记录各类型的活跃Job数量
            jobMetrics.recordActiveJobCount(JobType.API, apiJobCount);
            jobMetrics.recordActiveJobCount(JobType.SCRIPT, scriptJobCount);
            
            log.debug("更新活跃Job数量指标: total={}, api={}, script={}", 
                totalRunningJobs, apiJobCount, scriptJobCount);
                
        } catch (Exception e) {
            log.warn("更新活跃Job数量指标失败", e);
        }
    }
}