package com.xylink.manager.job.metrics;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.manager.service.nightingale.MonitorN9eService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * N9E指标推送器
 * 负责将Job监控指标推送到N9E监控系统
 */
@Component
@Slf4j
public class N9eMetricsPusher {
    
    @Autowired
    private MonitorN9eService monitorN9eService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 推送Job执行计数指标到N9E
     */
    public void pushExecutionCountMetric(String jobName, String jobType, String status, long count) {
        if (!monitorN9eService.nightingaleSwitch()) {
            return;
        }
        
        try {
            // 构建指标查询语句，用于验证指标是否存在
            String metricName = "job_execution_total";
            String query = String.format("%s{job_name=\"%s\",type=\"%s\",status=\"%s\"}", 
                metricName, jobName, jobType, status);
            
            // 记录指标到日志，N9E可以通过日志收集器收集这些指标
            log.info("JOB_METRIC: {}={{job_name=\"{}\",type=\"{}\",status=\"{}\"}} {}", 
                metricName, jobName, jobType, status, count);
                
        } catch (Exception e) {
            log.warn("推送Job执行计数指标失败: jobName={}, type={}, status={}, count={}", 
                jobName, jobType, status, count, e);
        }
    }
    
    /**
     * 推送Job执行时长指标到N9E
     */
    public void pushDurationMetric(String jobName, String jobType, long durationMs) {
        if (!monitorN9eService.nightingaleSwitch()) {
            return;
        }
        
        try {
            String metricName = "job_execution_duration_seconds";
            double durationSeconds = durationMs / 1000.0;
            
            // 记录指标到日志
            log.info("JOB_METRIC: {}={{job_name=\"{}\",type=\"{}\"}} {}", 
                metricName, jobName, jobType, durationSeconds);
                
        } catch (Exception e) {
            log.warn("推送Job执行时长指标失败: jobName={}, type={}, duration={}ms", 
                jobName, jobType, durationMs, e);
        }
    }
    
    /**
     * 推送活跃Job数量指标到N9E
     */
    public void pushActiveJobCountMetric(String jobType, int count) {
        if (!monitorN9eService.nightingaleSwitch()) {
            return;
        }
        
        try {
            String metricName = "job_active_count";
            
            // 记录指标到日志
            log.info("JOB_METRIC: {}={{type=\"{}\"}} {}", metricName, jobType, count);
                
        } catch (Exception e) {
            log.warn("推送活跃Job数量指标失败: type={}, count={}", jobType, count, e);
        }
    }
    
    /**
     * 推送锁获取指标到N9E
     */
    public void pushLockAcquisitionMetric(String jobName, String status, long count) {
        if (!monitorN9eService.nightingaleSwitch()) {
            return;
        }
        
        try {
            String metricName = "job_lock_acquired_total";
            
            // 记录指标到日志
            log.info("JOB_METRIC: {}={{job_name=\"{}\",status=\"{}\"}} {}", 
                metricName, jobName, status, count);
                
        } catch (Exception e) {
            log.warn("推送锁获取指标失败: jobName={}, status={}, count={}", 
                jobName, status, count, e);
        }
    }
    
    /**
     * 批量推送指标到N9E
     */
    public void pushMetricsBatch(Map<String, Object> metrics) {
        if (!monitorN9eService.nightingaleSwitch()) {
            log.debug("N9E监控未启用，跳过批量指标推送");
            return;
        }
        
        try {
            // 推送执行计数指标
            @SuppressWarnings("unchecked")
            Map<String, Long> executions = (Map<String, Long>) metrics.get("executions");
            if (executions != null) {
                executions.forEach((key, value) -> {
                    try {
                        parseAndPushExecutionMetric(key, value);
                    } catch (Exception e) {
                        log.warn("推送执行指标失败: key={}, value={}", key, value, e);
                    }
                });
            }
            
            // 推送活跃Job指标
            @SuppressWarnings("unchecked")
            Map<String, Long> activeJobs = (Map<String, Long>) metrics.get("activeJobs");
            if (activeJobs != null) {
                activeJobs.forEach((key, value) -> {
                    try {
                        parseAndPushActiveJobMetric(key, value);
                    } catch (Exception e) {
                        log.warn("推送活跃Job指标失败: key={}, value={}", key, value, e);
                    }
                });
            }
            
            // 推送锁获取指标
            @SuppressWarnings("unchecked")
            Map<String, Long> lockAcquisitions = (Map<String, Long>) metrics.get("lockAcquisitions");
            if (lockAcquisitions != null) {
                lockAcquisitions.forEach((key, value) -> {
                    try {
                        parseAndPushLockMetric(key, value);
                    } catch (Exception e) {
                        log.warn("推送锁获取指标失败: key={}, value={}", key, value, e);
                    }
                });
            }
            
            log.debug("批量推送Job指标到N9E完成");
            
        } catch (Exception e) {
            log.warn("批量推送Job指标到N9E失败", e);
        }
    }
    
    /**
     * 解析并推送执行指标
     * key格式: job_execution_total_jobName_type_status
     */
    private void parseAndPushExecutionMetric(String key, Long value) {
        String[] parts = key.split("_");
        if (parts.length >= 5) {
            String jobName = parts[3];
            String type = parts[4];
            String status = parts[5];
            pushExecutionCountMetric(jobName, type, status, value);
        }
    }
    
    /**
     * 解析并推送活跃Job指标
     * key格式: job_active_count_type
     */
    private void parseAndPushActiveJobMetric(String key, Long value) {
        String[] parts = key.split("_");
        if (parts.length >= 4) {
            String type = parts[3];
            pushActiveJobCountMetric(type, value.intValue());
        }
    }
    
    /**
     * 解析并推送锁获取指标
     * key格式: job_lock_acquired_total_jobName_status
     */
    private void parseAndPushLockMetric(String key, Long value) {
        String[] parts = key.split("_");
        if (parts.length >= 6) {
            String jobName = parts[4];
            String status = parts[5];
            pushLockAcquisitionMetric(jobName, status, value);
        }
    }
    
    /**
     * 查询N9E中的Job指标
     */
    public JsonNode queryJobMetrics(String metricName, String jobName) {
        if (!monitorN9eService.nightingaleSwitch()) {
            log.debug("N9E监控未启用，无法查询指标");
            return null;
        }
        
        try {
            String query;
            if (jobName != null && !jobName.isEmpty()) {
                query = String.format("%s{job_name=\"%s\"}", metricName, jobName);
            } else {
                query = metricName;
            }
            
            JsonNode result = monitorN9eService.query(query);
            log.debug("查询Job指标: metric={}, jobName={}, result={}", metricName, jobName, result);
            return result;
            
        } catch (Exception e) {
            log.warn("查询Job指标失败: metric={}, jobName={}", metricName, jobName, e);
            return null;
        }
    }
    
    /**
     * 获取N9E连接状态
     */
    public boolean isN9eAvailable() {
        try {
            return monitorN9eService.nightingaleSwitch();
        } catch (Exception e) {
            log.warn("检查N9E可用性失败", e);
            return false;
        }
    }
}