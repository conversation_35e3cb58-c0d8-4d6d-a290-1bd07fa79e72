package com.xylink.manager.job.metrics;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.util.Map;

/**
 * Job指标定时推送调度器
 * 定期将Job监控指标推送到N9E
 */
@Component
@Slf4j
public class JobMetricsScheduler {
    
    @Autowired
    private JobMetrics jobMetrics;
    
    /**
     * 每分钟推送一次指标到N9E
     */
    @Scheduled(fixedRate = 60000) // 60秒
    public void pushMetrics() {
        try {
            jobMetrics.pushMetricsToN9E();
        } catch (Exception e) {
            log.warn("定时推送Job指标失败", e);
        }
    }
    
    /**
     * 每小时输出一次指标摘要
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void logMetricsSummary() {
        try {
            Map<String, Object> summary = jobMetrics.getMetricsSummary();
            log.info("Job指标摘要: {}", summary);
        } catch (Exception e) {
            log.warn("输出Job指标摘要失败", e);
        }
    }
}