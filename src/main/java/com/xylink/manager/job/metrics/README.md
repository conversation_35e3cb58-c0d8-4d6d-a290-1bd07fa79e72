# Job监控指标系统

## 概述

Job监控指标系统为配置驱动的Job调度平台提供完整的监控指标收集和推送功能。系统复用现有的N9E监控配置，提供Job专用的监控指标，不影响现有业务指标。

## 核心组件

### 1. JobMetrics
主要的指标收集组件，负责：
- 记录Job执行次数（按状态分类）
- 记录Job执行时长统计
- 记录活跃Job数量
- 记录分布式锁获取统计
- 提供指标摘要查询

### 2. N9eMetricsPusher
N9E指标推送器，负责：
- 将指标推送到N9E监控系统
- 支持批量推送和单个推送
- 查询N9E中的Job指标
- 检查N9E系统可用性

### 3. JobMetricsScheduler
定时任务调度器，负责：
- 每分钟推送指标到N9E
- 每小时输出指标摘要日志

### 4. JobActiveCountUpdater
活跃Job数量更新器，负责：
- 每30秒更新活跃Job数量指标

## 支持的指标类型

### 1. job_execution_total
Job执行次数计数器
- 标签：job_name, type, status
- 示例：`job_execution_total{job_name="sync-user-data",type="API",status="success"} 15`

### 2. job_execution_duration_seconds
Job执行时长分布
- 标签：job_name, type
- 示例：`job_execution_duration_seconds{job_name="sync-user-data",type="API"} 2.5`

### 3. job_active_count
当前活跃Job数量
- 标签：type
- 示例：`job_active_count{type="API"} 3`

### 4. job_lock_acquired_total
分布式锁获取尝试次数
- 标签：job_name, status
- 示例：`job_lock_acquired_total{job_name="sync-user-data",status="success"} 10`

## REST API接口

### 获取指标摘要
```http
GET /jobs/metrics
```

响应示例：
```json
{
  "success": true,
  "data": {
    "executions": {
      "job_execution_total_sync-user-data_API_success": 15,
      "job_execution_total_sync-user-data_API_failure": 2
    },
    "durations": {
      "job_execution_duration_sync-user-data_API": {
        "count": 17,
        "totalMs": 42500,
        "avgMs": 2500,
        "minMs": 1200,
        "maxMs": 4800
      }
    },
    "activeJobs": {
      "job_active_count_API": 2,
      "job_active_count_SCRIPT": 1
    },
    "lockAcquisitions": {
      "job_lock_acquired_total_sync-user-data_success": 15,
      "job_lock_acquired_total_sync-user-data_failed": 2
    }
  }
}
```

### 重置指标
```http
POST /jobs/metrics/reset
```

### 手动推送指标到N9E
```http
POST /jobs/metrics/push
```

### 查询N9E中的Job指标
```http
GET /jobs/metrics/n9e?metric=job_execution_total&jobName=sync-user-data
```

## 配置要求

### N9E监控配置
系统会自动检查N9E监控是否启用：
- 通过`MonitorN9eService.nightingaleSwitch()`检查
- 如果N9E未启用，指标仍会收集但不会推送

### 日志配置
指标推送通过日志记录，确保logback配置包含Job相关的logger：
```xml
<logger name="com.xylink.manager.job.metrics" level="INFO" additivity="false">
    <appender-ref ref="JOB_EXECUTION"/>
</logger>
```

## 使用示例

### 在Job执行器中记录指标
```java
@Autowired
private JobMetrics jobMetrics;

public void executeJob(JobConfig jobConfig) {
    String jobName = jobConfig.getJobName();
    JobType jobType = jobConfig.getType();
    
    // 记录锁获取
    boolean lockAcquired = acquireDistributedLock(jobName);
    jobMetrics.recordLockAcquisition(jobName, lockAcquired);
    
    if (lockAcquired) {
        try {
            // 执行Job
            JobExecutionResult result = executeJobLogic(jobConfig);
            
            // 记录执行结果
            String status = result.isSuccess() ? "success" : "failure";
            jobMetrics.recordJobExecution(jobName, jobType, status);
            
            // 记录执行时长
            if (result.getDurationMs() != null) {
                jobMetrics.recordJobDuration(jobName, jobType, 
                    Duration.ofMillis(result.getDurationMs()));
            }
        } finally {
            releaseDistributedLock(jobName);
        }
    }
}
```

### 查询指标摘要
```java
@Autowired
private JobMetrics jobMetrics;

public void printMetricsSummary() {
    Map<String, Object> summary = jobMetrics.getMetricsSummary();
    log.info("Job指标摘要: {}", summary);
}
```

### 手动推送指标
```java
@Autowired
private JobMetrics jobMetrics;

public void pushMetrics() {
    if (jobMetrics.isN9eAvailable()) {
        jobMetrics.pushMetricsToN9E();
        log.info("指标推送完成");
    } else {
        log.warn("N9E监控不可用");
    }
}
```

## 监控告警建议

### 基于指标的告警规则
1. **Job执行失败率过高**
   ```
   rate(job_execution_total{status="failure"}[5m]) / rate(job_execution_total[5m]) > 0.1
   ```

2. **Job执行时长异常**
   ```
   job_execution_duration_seconds > 300
   ```

3. **锁获取失败率过高**
   ```
   rate(job_lock_acquired_total{status="failed"}[5m]) / rate(job_lock_acquired_total[5m]) > 0.2
   ```

4. **活跃Job数量异常**
   ```
   job_active_count > 10
   ```

## 性能考虑

### 内存使用
- 指标数据存储在内存中的ConcurrentHashMap
- 定期推送后数据仍保留，用于历史统计
- 可通过重置接口清理历史数据

### 线程安全
- 所有指标操作都是线程安全的
- 使用AtomicLong确保计数器的原子性
- 支持高并发场景下的指标记录

### 异常处理
- 指标记录失败不会影响Job执行
- 推送失败会记录警告日志但不抛出异常
- 提供降级机制，N9E不可用时仍可收集指标

## 扩展性

### 添加新指标类型
1. 在JobMetrics中添加新的计数器或统计器
2. 实现对应的记录方法
3. 在getMetricsSummary中包含新指标
4. 在N9eMetricsPusher中添加推送逻辑

### 自定义推送逻辑
可以通过实现自定义的MetricsPusher来支持其他监控系统：
```java
@Component
public class CustomMetricsPusher {
    public void pushMetrics(Map<String, Object> metrics) {
        // 自定义推送逻辑
    }
}
```

## 故障排查

### 常见问题
1. **指标未推送到N9E**
   - 检查N9E监控开关是否启用
   - 检查N9E服务连接状态
   - 查看推送相关的错误日志

2. **指标数据不准确**
   - 检查是否有并发问题
   - 验证指标记录的时机是否正确
   - 查看是否有异常导致指标记录失败

3. **内存使用过高**
   - 检查指标数据是否过多
   - 考虑定期重置历史指标
   - 优化指标key的命名规则

### 调试方法
1. 启用DEBUG日志级别查看详细信息
2. 使用/jobs/metrics接口查看当前指标状态
3. 通过/jobs/metrics/push手动触发推送测试
4. 检查N9E系统中是否收到指标数据