package com.xylink.manager.job.metrics;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.manager.job.model.JobConfig;
import com.xylink.manager.job.model.JobExecutionResult;
import com.xylink.manager.job.model.JobType;
import com.xylink.manager.service.nightingale.MonitorN9eService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Job监控指标组件
 * 负责收集和推送Job执行相关的监控指标到N9E
 */
@Component
@Slf4j
public class JobMetrics {
    
    @Autowired
    private MonitorN9eService monitorN9eService;
    
    @Autowired
    private N9eMetricsPusher n9eMetricsPusher;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    // 指标计数器
    private final Map<String, AtomicLong> executionCounters = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> lockAcquisitionCounters = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> activeJobCounters = new ConcurrentHashMap<>();
    
    // 执行时长统计
    private final Map<String, JobDurationStats> durationStats = new ConcurrentHashMap<>();
    
    /**
     * 记录Job执行次数
     */
    public void recordJobExecution(String jobName, JobType type, String status) {
        try {
            String key = buildMetricKey("job_execution_total", jobName, type.name(), status);
            executionCounters.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
            
            log.debug("记录Job执行指标: jobName={}, type={}, status={}, count={}", 
                jobName, type, status, executionCounters.get(key).get());
        } catch (Exception e) {
            log.warn("记录Job执行指标失败: jobName={}, type={}, status={}", jobName, type, status, e);
        }
    }
    
    /**
     * 记录Job执行时长
     */
    public void recordJobDuration(String jobName, JobType type, Duration duration) {
        try {
            String key = buildMetricKey("job_execution_duration", jobName, type.name());
            JobDurationStats stats = durationStats.computeIfAbsent(key, k -> new JobDurationStats());
            stats.record(duration.toMillis());
            
            log.debug("记录Job执行时长: jobName={}, type={}, duration={}ms", 
                jobName, type, duration.toMillis());
        } catch (Exception e) {
            log.warn("记录Job执行时长失败: jobName={}, type={}, duration={}", jobName, type, duration, e);
        }
    }
    
    /**
     * 记录活跃Job数量
     */
    public void recordActiveJobCount(JobType type, int count) {
        try {
            String key = buildMetricKey("job_active_count", type.name());
            activeJobCounters.computeIfAbsent(key, k -> new AtomicLong(0)).set(count);
            
            log.debug("记录活跃Job数量: type={}, count={}", type, count);
        } catch (Exception e) {
            log.warn("记录活跃Job数量失败: type={}, count={}", type, count, e);
        }
    }
    
    /**
     * 记录锁获取尝试
     */
    public void recordLockAcquisition(String jobName, boolean success) {
        try {
            String status = success ? "success" : "failed";
            String key = buildMetricKey("job_lock_acquired_total", jobName, status);
            lockAcquisitionCounters.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
            
            log.debug("记录锁获取尝试: jobName={}, success={}, count={}", 
                jobName, success, lockAcquisitionCounters.get(key).get());
        } catch (Exception e) {
            log.warn("记录锁获取尝试失败: jobName={}, success={}", jobName, success, e);
        }
    }
    
    /**
     * 推送指标到N9E
     */
    public void pushMetricsToN9E() {
        if (!monitorN9eService.nightingaleSwitch()) {
            log.debug("N9E监控未启用，跳过指标推送");
            return;
        }
        
        try {
            // 使用N9eMetricsPusher批量推送指标
            Map<String, Object> metrics = getMetricsSummary();
            n9eMetricsPusher.pushMetricsBatch(metrics);
            
            log.debug("Job指标推送到N9E完成");
        } catch (Exception e) {
            log.warn("推送Job指标到N9E失败", e);
        }
    }
    
    /**
     * 获取指标摘要信息
     */
    public Map<String, Object> getMetricsSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        // 执行次数统计
        Map<String, Long> executions = new HashMap<>();
        executionCounters.forEach((key, value) -> executions.put(key, value.get()));
        summary.put("executions", executions);
        
        // 时长统计
        Map<String, Map<String, Object>> durations = new HashMap<>();
        durationStats.forEach((key, stats) -> {
            Map<String, Object> statMap = new HashMap<>();
            statMap.put("count", stats.getCount());
            statMap.put("totalMs", stats.getTotalMs());
            statMap.put("avgMs", stats.getAverageMs());
            statMap.put("minMs", stats.getMinMs());
            statMap.put("maxMs", stats.getMaxMs());
            durations.put(key, statMap);
        });
        summary.put("durations", durations);
        
        // 活跃Job数量
        Map<String, Long> activeJobs = new HashMap<>();
        activeJobCounters.forEach((key, value) -> activeJobs.put(key, value.get()));
        summary.put("activeJobs", activeJobs);
        
        // 锁获取统计
        Map<String, Long> lockAcquisitions = new HashMap<>();
        lockAcquisitionCounters.forEach((key, value) -> lockAcquisitions.put(key, value.get()));
        summary.put("lockAcquisitions", lockAcquisitions);
        
        return summary;
    }
    
    /**
     * 重置所有指标
     */
    public void resetMetrics() {
        executionCounters.clear();
        lockAcquisitionCounters.clear();
        activeJobCounters.clear();
        durationStats.clear();
        log.info("Job监控指标已重置");
    }
    
    /**
     * 查询N9E中的Job指标
     */
    public JsonNode queryJobMetrics(String metricName, String jobName) {
        return n9eMetricsPusher.queryJobMetrics(metricName, jobName);
    }
    
    /**
     * 检查N9E是否可用
     */
    public boolean isN9eAvailable() {
        return n9eMetricsPusher.isN9eAvailable();
    }
    
    private String buildMetricKey(String metricName, String... labels) {
        StringBuilder key = new StringBuilder(metricName);
        for (String label : labels) {
            key.append("_").append(label);
        }
        return key.toString();
    }
    
    /**
     * Job执行时长统计类
     */
    private static class JobDurationStats {
        private long count = 0;
        private long totalMs = 0;
        private long minMs = Long.MAX_VALUE;
        private long maxMs = Long.MIN_VALUE;
        
        public synchronized void record(long durationMs) {
            count++;
            totalMs += durationMs;
            minMs = Math.min(minMs, durationMs);
            maxMs = Math.max(maxMs, durationMs);
        }
        
        public long getCount() { return count; }
        public long getTotalMs() { return totalMs; }
        public long getAverageMs() { return count > 0 ? totalMs / count : 0; }
        public long getMinMs() { return minMs == Long.MAX_VALUE ? 0 : minMs; }
        public long getMaxMs() { return maxMs == Long.MIN_VALUE ? 0 : maxMs; }
    }
}