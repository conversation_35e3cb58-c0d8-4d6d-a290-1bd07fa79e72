package com.xylink.manager.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Redis条件化配置
 * 
 * 只有在明确启用Redis功能时才会导入Redis相关的自动配置
 * 这样可以确保在不启用Redis时，Spring Boot不会尝试连接Redis
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "redis.enabled", havingValue = "true", matchIfMissing = false)
@Import({
    RedisAutoConfiguration.class,
    RedisReactiveAutoConfiguration.class,
    RedisRepositoriesAutoConfiguration.class
})
public class RedisConditionalConfiguration {
    // 这个类只是用来条件化导入Redis自动配置
    // 当redis.enabled=false时，这些自动配置都不会被加载
}
