package com.xylink.manager.model.em;

import com.google.common.collect.Lists;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.service.base.SystemModeConfig;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum Labels {

    k8smaster("k8s-master", true, false, false, false),
    etcd("etcd", true, true, false, true),
    main("main", true, false, false, false),
    main_partner("main-partner", true, false, false, false),
    openresty_main("openresty-main", true, false, true, true),
    openresty_internal("openresty-internal", true, false, true, true),
    pivotor("pivotor", true, false, false, true, NetworkConstants.PIVOTOR_IP),
    clientconfig("clientconfig", true, false, false, true, NetworkConstants.CLIENT_CONFIG_IP),
    css("css", true, false, false, true, NetworkConstants.CSS_IP),
    externalweb("externalweb", true, false, true, true, NetworkConstants.EXTERNALWEB_IP),
    signal("signal", true, false, true, true),
    proxysig("proxysig", true, false, false, true, NetworkConstants.PROXYSIG_IP),
    mms("mms", true, false, true, true),
    mc("mc", true, false, true, true),
    mc_proxy("mc-proxy", true, false, false, false),
    dmcu("dmcu", true, false, true, true),
    nmst("nmst", true, false, true, true),
    nmst_x86("nmst-x86", true, false, true, true),
    access("access", true, false, false, true, NetworkConstants.ACCESS_IP),
    accesssig("accesssig", true, false, false, true),
    uss("uss", true, false, true, true, NetworkConstants.USS_IP),
    iauth("iauth", true, false, false, true, NetworkConstants.IAUTH_IP),
    captcha("captcha", true, false, true, true, NetworkConstants.CAPTCHA_IP),
    userpermission("userpermission", true, false, false, true, NetworkConstants.USERPERMISSION_IP),
    callpermission("callpermission", true, false, false, true, NetworkConstants.CALLPERMISSION_IP),
    contact("contact", true, false, false, true, NetworkConstants.CONTACT_IP),
    contact_schedule("contact-schedule", true, false, false, true, ""),
    sitecode("sitecode", true, false, true, true, NetworkConstants.SITECODE_IP),
    vcs("vcs", true, false, false, true, NetworkConstants.VCS_IP),
    dating("dating", true, false, false, true, NetworkConstants.DATING_IP),
    charge          ("charge",true,false,true,true, NetworkConstants.CHARGE_IP),
    bill            ("bill",true,false,false,true, NetworkConstants.BILL_IP),
    charge_dispatcher("charge-dispatcher",true,false,false,true, ""),
    vote            ("vote",true,false,false,true, NetworkConstants.VOTE_IP),
    logserver       ("logserver",true,true,false,true, NetworkConstants.LOGSERVER_IP),
    vote_statistics ("vote-statistics",true,false,false,true, NetworkConstants.VOTE_STATISTICS_IP),
    im              ("im",true,false,false,true, NetworkConstants.IM_IP),

    mysql("mysql", true, true, true, false, Platform.X86),
    mysql_slave("mysqlslave", true, false, true),
    canal("canal", true, false, Platform.X86),
    database("database", true, true),
    st("shentong", false, false, Platform.ANKE),
    dm("dameng", false, false, true, false, Platform.ANKE),
    kingbase("kingbase", false, false, Platform.ANKE),
    oceanbase("oceanbase", false, false,false,false, Platform.ANKE),
//    oceanbase_cluster("oceanbase-cluster", false, false, true,true,Platform.ANKE),


    vod             ("vod",true,true, false, true, NetworkConstants.VOD_IP),
    record          ("record",true,false,true,true),
    rmserver        ("rmserver",true,false,false,true),
    record_x86          ("record-x86",true,false,true,true ,Platform.ANKE),

    redis("redis", true, false, true, true),
    redis_sentinel("redis-sentinel", false, false, false, true),

    statis          ("statis",true,true),
    statis_base          ("statis-base",true,true,false,true),
    zookeeper       ("zookeeper",true,false,true),
    zookeeper_cluster("zookeeper-cluster", false, false, false, true),
    hadoop_zookeeper_cluster ("hadoop-zookeeper-cluster", false, false,false,true),
    hbase("hbase", true, false, true),
    kafka("kafka", true, false, true),
    kafka_cluster("kafka-cluster", false, false, false, true),


    nfs("nfs", true, true),
    nfs_arm("nfs-arm", true, true, Platform.X86),
    sharing         ("sharing",true,false, false, true, NetworkConstants.SHARING_SERVER_IP),
    basicinfo("basicinfo", true, false, false, true, NetworkConstants.BASICINFO_IP),
    sdkcallback     ("sdkcallback", true, false, true, true, NetworkConstants.SDKCALLBACK_IP),
    inspection("inspection", true, false),
    presence        ("presence", true, false, false, true, NetworkConstants.PRESENCE_IP),
    nettool("nettool", true, false, true, true),
    nettool_arm("nettool-arm", true, false, true, true, Platform.X86),
    nettool_x86("nettool-x86", true, false, true, true, Platform.ANKE),
    cloud_meetingroom("cloudmeetingroom", true, false, true, true, NetworkConstants.CLOUD_MEETING_ROOM_IP),
    tsa("tsa", true, false, false, true, NetworkConstants.TSA_SERVER_IP),

    main_proxy("main-proxy", true, true, true, true),
    cloud_proxy("cloud-proxy", true, true, true, true),
    vod_proxy("vod-proxy", true, true, true, true),
    stream_proxy("stream-proxy", true, false, true, true, NetworkConstants.STREAM_PROXY_IP),
    ipip_proxy("ipip-proxy", true, false, true, true),
    push_proxy("push-proxy", true, false, false, true, NetworkConstants.PUSH_PROXY_IP),

    h323("h323", true, true, false, true),
    h323_arm("h323-arm", true, true, false, true, Platform.X86),
    sip("sip", true, true, false, true),
    h323_sig    ("h323-sig",true,false,false,true),
    h323_sig_arm    ("h323-sig-arm",true,false,false,true,Platform.X86),
    h323_media    ("h323-media",true,false,false,true),
    h323_media_arm    ("h323-media-arm",true,false,false,true,Platform.X86),
    avc_media     ("avc-media", true, false, true, true),
    avc_h323      ("avc-h323", true, false, false, true),
    avc_sip       ("avc-sip", false, false, false, true),
    avc           ("avc", true, false, false, true),
    avc_media_x86     ("avc-media-x86", true, false, true, true),
    avc_h323_x86      ("avc-h323-x86", true, false, false, true),
    avc_sip_x86       ("avc-sip-x86", false, false, true, true),
    avc_x86           ("avc-x86", true, false, false, true),
    ivr           ("ivr", true, false, true, true),
    ivr_x86           ("ivr-x86", true, false, true, true),

    avc_media_arm     ("avc-media-arm", true, false, true, true, Platform.X86),
    avc_h323_arm      ("avc-h323-arm", true, false, false, true, Platform.X86),
    avc_sip_arm       ("avc-sip-arm", false, false, true, true, Platform.X86),
    avc_arm           ("avc-arm", true, false, false, true, Platform.X86),
    ivr_arm           ("ivr-arm", true, false, true, true, Platform.X86),
    nmst_arm          ("nmst-arm", true, false, true, true, Platform.X86),

    h323_x86("h323-x86", true, true, false, true, Platform.ANKE),
    h323_sig_x86("h323-sig-x86", true, false, false, true, Platform.ANKE),
    h323_media_x86("h323-media-x86", true, false, false, true, Platform.ANKE),

    ma("ma", true, true, true, true),
    shuttle("shuttle", true, true, false, true),
    surv("surv", true, true),
    survbiz("survbiz", false, false),
    survsig("survsig", true, false),
    surv_access("surv-access", false, false),
    gather_mc("gather-mc", true, false),


    edu("edu", true, true),
    edu_gw("edu-gw", true, false, false, true),
    edu_file("edu-file", true, false, false, false),
    openresty_edu_file("openresty-edu-file", true, false, true, true),

    edu_hysiggw("edu-hysiggw", true, false, true, true),
    edu_hymediagw("edu-hymediagw", true, false, true, true),
    edu_1nsiggw("edu-1nsiggw", true, false, true, true),
    edu_1nmediagw("edu-1nmediagw", true, false, true, true),
    edu_1nsiggw_mgr("edu-1nsiggw-mgr", true, false, false, true),
    edu_1nsiggw_outproxy("edu-1nsiggw-outproxy", true, false, false, false),
    edu_1nmediagw_outproxy("edu-1nmediagw-outproxy", true, false, false, false),

    edu_adapter     ("edu-adapter",true,false,false,true,NetworkConstants.EDU_ADAPTER_IP),
    edu_dating      ("edu-dating",true,false,false,true,NetworkConstants.EDU_DATING_IP),
    edu_manage      ("edu-manage",true,false,false,true,NetworkConstants.EDU_MANAGE_IP),
    edu_resource    ("edu-resource",true,false,false,true,NetworkConstants.EDU_RESOURCE_IP),
    education       ("education",true,false,false,true,NetworkConstants.EDUCATION_IP),
    examination     ("examination",true,false,false,true,NetworkConstants.EXAMINATION_IP),
    file_manage     ("file-manage",true,false,false,true,NetworkConstants.FILE_MANAGE_IP),
    file_manage_ud  ("file-manage-ud",true,false,true,true,NetworkConstants.FILE_MANAGE_UD_IP),
    message_push    ("message-push",true,false,false,true,NetworkConstants.MESSAGE_PUSH_IP),
    edu_vodshare    ("edu-vodshare",false,false,false,false),

    matrix("matrix", true, true),
    matrix_alg("matrix-alg", true, false),

    ippbx("ippbx", true, true),
    ippbx_siggw("ippbx-siggateway", true, false, true, true),
    ippbx_mediagw("ippbx-mediagw", true, false, true, true),

    dns_inner("dnsinter", true, false),
    dns_outer("dnsouter", true, false),
    thirdadapter("thirdadapter", true, false, false, true, NetworkConstants.THIRDADAPTER_IP),
    third_bridge("third-bridge", true, false,true,true, NetworkConstants.THIRD_BRIDGE_IP),
    third_bridge_dameng("third-bridge-dameng", true, false,false,false),
    third_bridge_mysql("third-bridge-mysql", true, false,false,false),
    frontend_third_bridge("frontend-third-bridge", true, false,false,true, NetworkConstants.THIRD_BRIDGE_FRONTEND_IP),
    third_proxy("third-proxy", true, false,true,true),

    meetingmonitor  ("meetingmonitor",true,false,false,true,NetworkConstants.MEETING_MONITOR_IP),
    ocean           ("ocean", true, false, false, true, NetworkConstants.OCEAN_IP),
    mongodb         ("mongodb",true,false),

    ntpd("ntpd", true, false),

    charge_redis("charge-redis", true, false),


    clickhouse("clickhouse", true, false),
    statis_education("statis-education", true, false),
    statis_meeting("statis-meeting", true, false),
    statis_monitor("statis-monitor", true, false),
    statis_quality("statis-quality", true, false),
    statis_live("statis-live", true, false),
    statis_dameng("statis-dameng", true, false),
    statis_shentong("statis-shentong", true, false),
    meeting_quality("meeting-quality", true, false, false, true),


    webrtc("webrtc", true, true),
    uaa("uaa", true, true),
    openresty_webrtc("openresty-webrtc", false, false, true, true),
    uaa_base("uaa-base", true, false, false, true,NetworkConstants.UAA_BASE_IP),
    uaa_api("uaa-api", true, false, false, true, NetworkConstants.UAA_API_IP),
    uaa_mysql("uaa-mysql", true, false, true, false, Platform.X86),
    uaa_mysqlslave("uaa-mysqlslave",true,false,true),

    webrtc_siggw("webrtc-siggw", false, false, true, true),
    webrtc_mediagw("webrtc-mediagw", true, false, true, true),
    allocator_server("allocatorserver", false, false, true, true, NetworkConstants.ALLOCATOR_IP),
    //webrtc_tsa      ("webrtc-tsa", true, false, false, true, NetworkConstants.WEBRTC_TSA_IP),
    pinpoint("pinpoint", true, false),
    sensitiveword("sensitiveword", true, false),
    es("es", true, false, false, false, NetworkConstants.ES_IP),

    // 会议纪要
    transcript("transcript", true, true),
    transcription("transcription", true, false, true, false),
    asralg("asralg", true, false, false, true),
    asralg2("asralg2", true, false, false, true),
    aiagent("aiagent", true, false, true, true, NetworkConstants.AIAGENT_IP),
    aibusiness("aibusiness", true, false, false, true,NetworkConstants.AIBUSINESS_IP),

    xyai("xyai", true, true, false, true),
    aicontroller("aicontroller", true, false, false, true, NetworkConstants.AICONTROLLER_IP),
    aigateway("aigateway", true, false, true, true,NetworkConstants.AIGATEWAY_IP),

    // 前端
    frontend        ("frontend",false,false,false,true),
    frontend_buffet("frontend-buffet",false,false,false,true),
    frontend_meetingschedule("frontend-meetingschedule",false,false,false,true),
    frontend_meeting("frontend-meeting",false,false,false,true),
    frontend_pcclient("frontend-pcclient",false,false,false,true),

    frontend_buffet_x86("frontend-buffet-x86", Platform.ANKE),
    frontend_meetingschedule_x86("frontend-meetingschedule-x86", Platform.ANKE),
    frontend_meeting_x86("frontend-meeting-x86", Platform.ANKE),
    frontend_pcclient_x86("frontend-pcclient-x86", Platform.ANKE),

    frontend_buffet_arm("frontend-buffet-arm", Platform.X86),
    frontend_meetingschedule_arm("frontend-meetingschedule-arm", Platform.X86),
    frontend_meeting_arm("frontend-meeting-arm", Platform.X86),
    frontend_pcclient_arm("frontend-pcclient-arm", Platform.X86),
    // hadoop
    hadoop("hadoop", true, true),
    azkaban("azkaban", false, false, false, false),
    yarn("yarn", false, false, false, false),
    hadoop_db("hadoop-db", true, false, false, false),
    hadoop_master("hadoop-master", true, false, false, true),
    hadoop_single("hadoop-single", true, false, false, false),
    hadoop_node("hadoop-node", true, false, false, true),
    hadoop_cluster("hadoop-cluster", false, false, false, true),
    hive_mysql("hive-mysql", true, false, false, false),

    // liveness
    liveness_probe("liveness-probe", true, false, false, true, NetworkConstants.LIVENESS_IP),
    // live
    live("live", false, false, true, true, NetworkConstants.LIVE_IP),
    srs("srs", false, false, false, false, NetworkConstants.SRS_IP),

    srs_extends("srs-extends", true, false, false, true),
    srs_extends_proxy("srs-extends-proxy", true, false, true, true),

    //haproxy
    haproxy("haproxy", true, false, true, true),
    haproxy_arm("haproxy-arm", true, false, true, true, Platform.X86),
    frontend_x86("frontend-x86", Platform.ANKE),
    dmcu_x86("dmcu-x86", true, false, true, true, Platform.ANKE),
    main_proxy_x86("main-proxy-x86", true, false, true, true, Platform.ANKE),
    vod_proxy_x86("vod-proxy-x86", true, false, true, true, Platform.ANKE),

    frontend_arm("frontend-arm", Platform.X86),
    dmcu_arm("dmcu-arm", true, false, true, true, Platform.X86),
    dmcu_side("dmcu-side", true, false, true, true),
    dmcu_side_x86("dmcu-side-x86", true, false, true, true, Platform.ANKE),
    main_proxy_arm("main-proxy-arm", true, false, true, true, Platform.X86),
    vod_proxy_arm("vod-proxy-arm", true, false, true, true, Platform.X86),
    record_arm("record-arm", true, false, true, true, Platform.X86),

    // 内网大规模直播
    hls("hls", true, false, true, true),
    hls_proxy("hls-proxy", true, false, true, true),
    mcaccess("mcaccess", true, false, true, true,NetworkConstants.MCACCESS_IP),
    avcloudapi("avcloudapi", true, false, false, true,NetworkConstants.AVCLOUDAPI_IP),

    // RTMP接入
    basic_management("basic-management", true, false, false, true, NetworkConstants.BASIC_MANAGEMENT_IP),
    converged_mediagw("converged-mediagw", true, false, true, true),
    converged_siggw("converged-siggw", true, false, false, true),
    rtmp("rtmp", true, false, false, true),

    sdk_file("sdk-file", true, false, false, false),
    sip_server("sip-server", false, false, false, true, NetworkConstants.SIP_SERVER_IP),
    sip_media("sip-media", true, false, true, true),
    gatekeeper("gatekeeper", true, false, false, true),
    gatekeeper_x86("gatekeeper-x86", true, false, false, true),
    gatekeeper_arm("gatekeeper-arm", true, false, false, true),

    /**
     * 大规模内网点播
     */
    vodnetwork("vodnetwork", true, true, false, true),
    vodnetwork_vod("vodnetwork-vod", true, false, true, true),
    vodnetwork_vodedit("vodnetwork-vodedit", true, false, true, true),
    vodnetwork_proxy("vodnetwork-proxy", true, false, true, true),
    vodclustermgr("vodclustermgr", true, false, false, true, NetworkConstants.VODCLUSTERMGR_IP),

    vodnetwork_arm("vodnetwork-arm", true, true, false, true, Platform.X86),
    vodnetwork_vod_arm("vodnetwork-vod-arm", true, false, true, true, Platform.X86),
    vodnetwork_vodedit_arm("vodnetwork-vodedit-arm", true, false, true, true, Platform.X86),
    vodnetwork_proxy_arm("vodnetwork-proxy-arm", true, false, true, true, Platform.X86),

    sms("sms", true, true, true, true, NetworkConstants.SMS_IP),
    dcm("dcm", true, false, false, true, NetworkConstants.DCM_SERVER_INTERNAL_IP),
    tsa_mp("tsa-mp", true, false, false, true),
    vpnserver("vpnserver", true, false, false, false),
    vpnserver_x86("vpnserver-x86", true, false, false, false, Platform.ANKE),
    node_proxy("node-proxy", true, false, false, true),
    node_proxy_x86("node-proxy-x86", true, false, false, true, Platform.ANKE),

    /**
     * sdk微信小程序模块
     */
    nmsa("nmsa", true, true, true, true),
    nmsa_proxy("nmsa-proxy", false, false, true, true),
    txrest("txrest", true, true, false, false),
    wxrtc_proxy("wxrtc-proxy", true, false, true, false),
    txlive("txlive", true, false, true, true),

    webrtc_x86("webrtc-x86", true, true, false, false, Platform.ANKE),
    openresty_webrtc_x86("openresty-webrtc-x86", false, false, true, true, Platform.ANKE),
    webrtc_siggw_x86("webrtc-siggw-x86", false, false, true, true, Platform.ANKE),
    webrtc_mediagw_x86("webrtc-mediagw-x86", true, false, true, true, Platform.ANKE),

    webrtc_arm("webrtc-arm", true, true, false, false, Platform.X86),
    openresty_webrtc_arm("openresty-webrtc-arm", false, false, true, true, Platform.X86),
    webrtc_siggw_arm("webrtc-siggw-arm", false, false, true, true, Platform.X86),
    webrtc_mediagw_arm("webrtc-mediagw-arm", true, false, true, true, Platform.X86),

    cdn_proxy("cdn-proxy", true, false, true, true),
    push("push", true, true, true, false),
    vod_poc("vod-poc",true,true,true,true),
    /**
     * 跨云级联
     **/
    cascade("cascade", false, true, false, true),
    cascadegw("cascadegw", true, false, true, true),
    cascademgr("cascademgr", true, false, true, true, NetworkConstants.CASCADEMGR_IP),
    avstatusserver("avstatusserver", true, false, false, true),
    roster("roster", true, false, false, true),
    basic_xyauth("basic-xyauth", true, false, false, true, NetworkConstants.BASIC_XYAUTH_IP),
    data_transfer_server("data-transfer-server", true, false, false, true, NetworkConstants.DATA_TRANSFER_SERVER_IP),
    openresty_vod("openresty-vod", false, false, false, true),
    /**
     * 52通用适配高可用
     **/
    middleware("middleware", true, false, false, true),
    call_base("call-base", true, false, false, true),
    call_sig("call-sig", true, false, false, true),
    basic_base("basic-base", true, false, false, true),
    charge_base("charge-base", true, false, false, true),
    basic_charge("basic-charge", true, false, false, true),
    matrix_transcript("matrix-transcript", true, false, false, false),
    master("master", true, true),
    signal_mc("signal-mc", true, false, false, true),
    mysql_ha("mysql-ha", true, false, false, true),
    ippbx_siggateway("ippbx-siggateway", true, false, false, true),
    ippbx_mediagateway("ippbx-mediagateway", true, false, false, true),
    vod_base("vod-base", true, false, false, true,NetworkConstants.VOD_BASE_INTERNAL_IP),

    vodfilemanager("vodfilemanager", true, true, true, true, NetworkConstants.VOD_FILEMANAGER_IP),
    vodmanager("vodmanager", true, true, false, true, NetworkConstants.VOD_MANAGER_IP),
    msg_server("msgserver", true, false, false, true),
    devicestate("devicestate", true, false, false, true),
    buffet("buffet", true, true, false, true, NetworkConstants.BUFFET_IP),
    page("page", true, true, false, true, NetworkConstants.PAGE_IP),
    business_download_service("business-download-service", true, false, false, true, NetworkConstants.BUSINESS_DOWNLOAD_SERVICE_IP),
    live_audience("live-audience", true, false, false, true, NetworkConstants.LIVE_AUDIENCE_IP),
    vod_share   ("vodshare", true, true, false, true, NetworkConstants.VOD_SHARE_IP),
    nodelive("nodelive", true, false, false, true, NetworkConstants.LIVE_FRONT_IP),
    vod_fileserver("vod-fileserver", true, true, false, false),
    datafact("datafact", true, false, false, false),
    dcs("dcs", true, false, false, false),
    des("des", true, false, false, false),
    data_event_collector("data-event-collector", true, false, false, true,NetworkConstants.DEC_SERVER_INTERNAL_IP),
    statis_mysql("statis-mysql", true, false),
    vod_edit   ("vodedit", true, true, false, true, NetworkConstants.VOD_EDIT_IP),
    aistorage   ("aistorage", true, true, false, true, NetworkConstants.AISTORAGE_IP),


    /**
     * mms
     **/

    /**
     * 融合会管fusion-mms
     */
    /**
     * 融合会管fusion-mms
     */
    fusion_mms("fusion-mms", true, false, false, true, NetworkConstants.FUSION_MMS_IP),
    openresty_fusion       ("openresty-fusion",true,false,true,true),
    frontend_fusion       ("frontend-fusion",true,false,true,true),
    fusion_uaa_api("fusion-uaa-api", true, false, false, true, NetworkConstants.FUSION_UAA_API_IP),
    fusion_uaa_base("fusion-uaa-base", true, false, false, true, NetworkConstants.FUSION_UAA_BASE_IP),
    fusion_uaa_admin("fusion-uaa-admin", true, false, false, true, NetworkConstants.FUSION_UAA_ADMIN_IP),
    nightingale("nightingale",true, false, false, true, NetworkConstants.N9E_IP),
    nightingale_categraf_new("nightingale-categraf-new",true, false, false, true),
    nightingale_categraf("nightingale-categraf",true, false, true, true),
    nightingale_mid("nightingale-mid",true, false, false, true),
    kube_state_metrics("kube-state-metrics",true, false, false, true),
    nightingale_kafka("nightingale-kafka",true, false, false, true),
    nightingale_zookeeper("nightingale-zookeeper",true, false, false, true),

    //调用链系统
    jaeger("jaeger",true, false, false, false),

    ams_proxy("ams-proxy",true,true,false,true),
    cascadegw_base("cascadegw-base",true,true,false,true),
    proxy_base("proxy-base",true,true,false,true, NetworkConstants.PROXY_BASE_IP),

    webrtc_proxy("webrtc-proxy",true,true,true,true),
    webrtc_proxy_arm("webrtc-proxy-arm",true,true,true,true,Platform.X86),
    webrtc_proxy_x86("webrtc-proxy-x86",true,true,true,true,Platform.ANKE),

    /**
     * 建行merge代码新增服务
     */
    nightalarm("nightalarm",true, false, false, true,NetworkConstants.NIGHTALARM_IP),
    ;

    private String label;
    /**
     * 是否支持单独拆分部署
     */
    private boolean single;
    /**
     * 是否在简单部署列表中展示
     */
    private boolean simple;

    /**
     * 是否提供高级配置拓展
     */
    private boolean advance;

    /**
     * 是否支持多点部署
     */
    private boolean distributed;

    /**
     * all-ip中记录该服务的Ip Key， 用于做负载均衡
     */
    private String ip;

    /**
     * 支持平台 x86 \ anke 默认 x86
     */
    private Set<String> platform = new HashSet<>();

    Labels() {
        this.platform.addAll(Platform.ALL.platform());
    }

    Labels(String label) {
        this();
        this.label = label;
        this.single = false;
        this.simple = false;
        this.advance = false;
        this.distributed = false;
        this.ip = null;

    }

    Labels(String label, Platform platform) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform must not null.");
        }
        this.label = label;
        this.single = false;
        this.simple = false;
        this.advance = false;
        this.distributed = false;
        this.ip = null;
        this.platform = platform.platform();

    }

    Labels(String label, boolean single, boolean simple) {
        this();
        this.label = label;
        this.single = single;
        this.simple = simple;
        this.advance = false;
        this.distributed = false;
        this.ip = null;

    }

    Labels(String label, boolean single, boolean simple, Platform platform) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform must not null.");
        }
        this.label = label;
        this.single = single;
        this.simple = simple;
        this.advance = false;
        this.distributed = false;
        this.ip = null;
        this.platform = platform.platform();

    }

    Labels(String label, boolean single, boolean simple, boolean advance) {
        this();
        this.label = label;
        this.single = single;
        this.simple = simple;
        this.advance = advance;
        this.distributed = false;
        this.ip = null;

    }

    Labels(String Label, boolean single, boolean simple, boolean advance, boolean distributed) {
        this();
        this.label = Label;
        this.single = single;
        this.simple = simple;
        this.advance = advance;
        this.distributed = distributed;
        this.ip = null;

    }

    Labels(String Label, boolean single, boolean simple, boolean advance, boolean distributed, Platform platform) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform must not null.");
        }
        this.label = Label;
        this.single = single;
        this.simple = simple;
        this.advance = advance;
        this.distributed = distributed;
        this.ip = null;
        this.platform = platform.platform();
    }

    Labels(String Label, boolean single, boolean simple, boolean advance, boolean distributed, String ip) {
        this();
        this.label = Label;
        this.single = single;
        this.simple = simple;
        this.advance = advance;
        this.distributed = distributed;
        this.ip = ip;
    }


    public String label() {
        return this.label;
    }

    private boolean isSingle() {
        return this.single;
    }

    private boolean isSimple() {
        return this.simple;
    }

    private boolean isAdvance() {
        return this.advance;
    }

    public boolean isDistributed() {
        return this.distributed;
    }

    public String loadblanceKey() {
        return this.ip;
    }

    public static List<String> all() {
        return Arrays.stream(Labels.values()).map(Labels::label).collect(Collectors.toList());
    }

    public static List<String> nodes(Platform platform) {
        List<Labels> allNode = Arrays.stream(Labels.values()).filter(Labels::isSingle).collect(Collectors.toList());
        List<String> labelList = Platform.ALL.equals(platform) ?
                allNode.stream().map(Labels::label).collect(Collectors.toList()) :
                allNode.stream().filter(item -> item.platform.contains(platform.name())).map(Labels::label).collect(Collectors.toList());
        if (SystemModeConfig.isCmsOrXms()) {
            labelList.add(Constants.NODE_TYPE_COMMON_MAIN);
            labelList.add(Constants.NODE_TYPE_COMMON_NODE);
        }
        return labelList;
    }

    public static List<String> advanceLabels() {
        return Arrays.stream(Labels.values()).filter(Labels::isAdvance).map(Labels::label).collect(Collectors.toList());
    }

    public static List<String> simpleNodes(Platform platform) {
        List<Labels> allNode = Arrays.stream(Labels.values()).filter(Labels::isSimple).collect(Collectors.toList());
        return Platform.ALL.equals(platform) ?
                allNode.stream().map(Labels::label).collect(Collectors.toList()) :
                allNode.stream().filter(item -> item.platform.contains(platform.name())).map(Labels::label).collect(Collectors.toList());
    }

    public static List<String> distributedLabels() {
        return Arrays.stream(Labels.values()).filter(Labels::isDistributed).map(Labels::label).collect(Collectors.toList());
    }

    public static Labels labelOf(String label) {
        return Arrays.stream(Labels.values()).filter(la -> la.label().equals(label)).findFirst().orElseThrow(() -> new ServerException(ErrorStatus.ERROR_LABEL));
    }

    /**
     * 信创混合部署，x86机器上部署x86服务标签
     *
     * @return
     */
    public static List<String> x86Nodes() {
        return Lists.newArrayList(dmcu_x86.label(), main_proxy_x86.label(), record_x86.label(), h323_x86.label(), h323_media_x86.label(), h323_sig_x86.label(), vod_proxy_x86.label(), webrtc_x86.label(), webrtc_mediagw_x86.label(), webrtc_siggw_x86.label(), vpnserver_x86.label(), node_proxy_x86.label(), avc_h323_x86.label(), avc_x86.label(), avc_media_x86.label(), avc_sip_x86.label(), gatekeeper_x86.label(), nmst_x86.label(), ivr_x86.label());
    }

    /**
     * 通用混合部署，arm机器上部署arm服务标签
     *
     * @return
     */
    public static List<String> ArmNodes() {
        return Lists.newArrayList(haproxy_arm.label(), main_proxy_arm.label(), record_arm.label(), vod_proxy_arm.label(), vodnetwork_proxy_arm.label(), vodnetwork_arm.label(), vodnetwork_vod_arm.label(), vodnetwork_vodedit_arm.label(), dmcu_arm.label(), webrtc_arm.label(), webrtc_mediagw_arm.label(), webrtc_siggw_arm.label(),h323_arm.label(), h323_sig_arm.label(), h323_media_arm.label(), avc_arm.label(), avc_h323_arm.label(), avc_media_arm.label(), ivr_arm.label(), nmst_arm.label(), avc_sip_arm.label(), gatekeeper_arm.label());
    }

}
