package com.xylink.manager.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.escape.Escaper;
import com.google.common.net.UrlEscapers;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.LogAgentConfig;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.DaemonSet;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.base.impl.K8sThirdDeployService;
import com.xylink.util.Ipv6Util;
import com.xylink.util.MemoryPaginationUtil;
import com.xylink.util.SearchDtoUtil;
import com.xylink.util.SecurityContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;

/**
 * Created by liujian on 2018/8/10.
 */
@Service
public class ServerLogService {
    private final static Logger logger = LoggerFactory.getLogger(ServerLogService.class);

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ServerListService serverListService;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private K8sSvcService k8sSvcService;
    @Autowired
    IDeployService k8sThirdDeployService;
    @Autowired
    private IDeployService deployService;
    @Autowired
    private ObjectMapper objectMapper;
    @Value("${main.log.dir}")
    private String MAIN_SERVER_LOG_ROOT;
    private String MAIN_SERVER_HADOOP_LOG_ROOT;

    private String MAIN_CLIENT_LOG_ROOT;

    private static final String serverLogPrefix = "/logagent/server";
    public static final String CLIENT_LOG_PREFIX = "/logagent/client";

    @PostConstruct
    private void loadDir() {
        MAIN_CLIENT_LOG_ROOT = StringUtils.isBlank(k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("clientLogUploadDir")) ? "/mnt/xylink/logs/client/" : k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("clientLogUploadDir");
    }

    @Value("${base.dir}")
    public void setBaseDir(String baseDir) {
        MAIN_SERVER_HADOOP_LOG_ROOT = baseDir + "/hadoop/logs/";
    }

    /**
     * 在日志下载页面获取服务节点信息，包括节点名、内网ip、节点类型
     *
     * @return
     */
    public Page<NodeDto> getLogServerNodeList(String key, long pageSize, long current) {
        logger.info("getLogServerNodeList, key: {}, pageSize: {}, current: {}", key, pageSize, current);
        List<NodeDto> res = new ArrayList<>();
        Map<String, Map<String, String>> nodeTypeToCM = new HashMap<>();
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        List<String> distributedLabellist = Labels.distributedLabels();

        List<Node> nodeList = serverListService.getNodeListFilterByUser(SecurityContextUtil.currentUser());
        logger.info("getLogServerNodeList, nodeList size: {}", nodeList.size());
        if (StringUtils.isBlank(key)) {
            Page<Node> pager = MemoryPaginationUtil.pagination(nodeList, current, pageSize);
            for (Node node : pager.getRecords()) {
                NodeDto oneNodeDto = generateNodeDto(node, allIp, distributedLabellist, nodeTypeToCM);
                res.add(oneNodeDto);
            }
            return new Page<>(current, pageSize, nodeList.size(), res);
        }

        for (Node item : nodeList) {
            NodeDto nodeDto = generateNodeDto(item, allIp, distributedLabellist, nodeTypeToCM);
            res.add(nodeDto);
        }
        logger.info("getLogServerNodeList, res size: {}", res.size());
        List<NodeDto> records = SearchDtoUtil.searchKeyWord(res, key);
        logger.info("getLogServerNodeList, records size: {}", records.size());
        return MemoryPaginationUtil.pagination(records, current, pageSize);
    }

    private NodeDto generateNodeDto(Node item, Map<String, String> allIp, List<String> distributedLabelList, Map<String, Map<String, String>> nodeTypeToCM) {
        NodeDto nodeDto = new NodeDto();
        nodeDto.setName(item.getName());
        nodeDto.setType(item.getType());
        nodeDto.setReportInternalIp(item.getIp());
        nodeDto.setInternalIp(nodeDto.getReportInternalIp());
        if (Constants.NODE_TYPE_COMMON_MAIN.equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.COMMON_MAIN_INTERNAL_IP));
        } else if (Constants.NODETYPE_MAIN.equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.MAIN_INTERNAL_IP));
        } else if (Labels.vod.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.VOD_INTERNAL_IP));
        } else if (Labels.surv.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.SURV_INTERNAL_IP));
        } else if (Labels.ippbx.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.IPPBX_INTERNAL_IP));
        } else if (Labels.dns_inner.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.INNER_DNS_INTERNAL_IP));
        } else if (Labels.dns_outer.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.OUTER_DNS_INTERNAL_IP));
        }else if (Labels.cascade.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.CASCADEGW_IP));
        } else if (Labels.vod_poc.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.VOD_INTERNAL_IP));
        }

        if (StringUtils.isNotBlank(nodeDto.getType())) {
            if (StringUtils.isNotEmpty(Constants.interIps.get(nodeDto.getType()))) {
                nodeDto.setInternalIp(allIp.get(Constants.interIps.get(nodeDto.getType())));
            } else if (distributedLabelList.contains(nodeDto.getType())
                    || nodeDto.getType().equalsIgnoreCase(Labels.main_partner.label())
                    || Constants.NODE_TYPE_COMMON_MAIN.equalsIgnoreCase(nodeDto.getType())
                    || Constants.NODETYPE_MAIN.equalsIgnoreCase(nodeDto.getType())) {
                // dmcu-arm 的内外网/域名信息 从all-dmcu读取
                String nodeType = nodeDto.getType();
                if (nodeDto.getType().equalsIgnoreCase(Labels.dmcu_x86.label()) || nodeDto.getType().equalsIgnoreCase(Labels.dmcu_arm.label()) || nodeDto.getType().equalsIgnoreCase(Labels.dmcu_side.label()) || nodeDto.getType().equalsIgnoreCase(Labels.dmcu_side_x86.label())) {
                    nodeType = Labels.dmcu.label();
                } else if (nodeDto.getType().equalsIgnoreCase(Labels.main_proxy_x86.label()) || nodeDto.getType().equalsIgnoreCase(Labels.main_proxy_arm.label())) {
                    nodeType = Labels.main_proxy.label();
                } else if (nodeDto.getType().equalsIgnoreCase(Labels.record_x86.label()) || nodeDto.getType().equalsIgnoreCase(Labels.record_arm.label())) {
                    nodeType = Labels.record.label();
                } else if (nodeDto.getType().equalsIgnoreCase(Labels.h323_x86.label()) || nodeDto.getType().equalsIgnoreCase(Labels.h323_arm.label())) {
                    nodeType = Labels.h323.label();
                } else if (nodeDto.getType().endsWith("-x86")) {
                    nodeType = nodeDto.getType().replace("-x86", "");
                } else if (nodeDto.getType().endsWith(Constants.ARM)) {
                    nodeType = nodeDto.getType().replace(Constants.ARM, "");
                }
                if (Labels.h323_sig.label().equalsIgnoreCase(nodeDto.getType())
                        || Labels.h323_sig_x86.label().equalsIgnoreCase(nodeDto.getType())
                        || Labels.h323_sig_arm.label().equalsIgnoreCase(nodeDto.getType())) {
                    nodeType = "h323-gateway";
                }

                if (Constants.NODETYPE_MAIN.equalsIgnoreCase(nodeDto.getType())) {
                    nodeType = "openresty-main";
                }

                String key = "all-" + nodeType;
                Map<String, String> configMapData = nodeTypeToCM.get(key);
                if (CollectionUtils.isEmpty(configMapData)) {
                    configMapData = k8sService.getConfigmapOrCreate(key);
                    nodeTypeToCM.put(key, configMapData);
                }
                String nodeInterIpKey = item.getName() + NetworkConstants.SUFFIX_INTERNAL_IP;
                nodeDto.setInternalIp(configMapData.get(nodeInterIpKey));
            } else if (Labels.transcript.label().equalsIgnoreCase(nodeDto.getType())) {
                nodeDto.setInternalIp(allIp.get(NetworkConstants.TRANSCRIPTION_INTERNAL_IP));
            }

        }

        if (StringUtils.isBlank(nodeDto.getInternalIp())) {
            nodeDto.setInternalIp(nodeDto.getReportInternalIp());
        }

        return nodeDto;
    }

    public ArrayNode getlogInfoByPath(String nodeName, String logPath) {
        logger.info("nodeName: {}, logPath: {}", nodeName, logPath);
        com.xylink.manager.model.deploy.Node node = deployService.getNodeByName(nodeName);
        if (node == null) {
            return objectMapper.createArrayNode();
        }
        String nodeIp = node.getIp();
        if (StringUtils.isEmpty(nodeIp))
            return objectMapper.createArrayNode();
        try {
            String logAgentIp = k8sSvcService.getLogAgentPodIpByNodeIp(nodeIp);
            if (StringUtils.isBlank(logAgentIp)) {
                return objectMapper.createArrayNode();
            }
            String nodeLogUrl = "http://" + Ipv6Util.handlerIpv6Addr(logAgentIp) + ":" + NetworkConstants.LOGAGENT_PORT + serverLogPrefix + logPath;
            logger.info("nodeLogUrl: " + nodeLogUrl);
            String jsonStr = restTemplate.getForObject(nodeLogUrl, String.class);
            logger.info(jsonStr);

            return filterRootInfo(jsonStr, MAIN_SERVER_LOG_ROOT, MAIN_SERVER_HADOOP_LOG_ROOT);
        } catch (Exception e) {
            logger.info(ExceptionUtils.getStackTrace(e));
        }
        return objectMapper.createArrayNode();
    }

    public void downloadServerLog(String nodeName, String logPath, HttpServletResponse response) {
        com.xylink.manager.model.deploy.Node node = deployService.getNodeByName(nodeName);
        if (node == null) {
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
        String nodeIp = node.getIp();
        String fileName = logPath.substring(logPath.lastIndexOf("/") + 1);
        logger.info("download fileName: " + fileName);
        downloadLog(logPath, serverLogPrefix, response, nodeIp, fileName);
    }

    public void downloadClientLog(String logPath, HttpServletResponse response) {
        com.xylink.manager.model.deploy.Node node = deployService.listNodesByLabels(Constants.TYPE, "main").get(0);
        if (node == null) {
            return;
        }
        String nodeIp = node.getIp();
        String fileName = logPath.substring(logPath.lastIndexOf("/") + 1);
        logger.info("download fileName: " + fileName);

        downloadLog(logPath, CLIENT_LOG_PREFIX, response, nodeIp, fileName);
    }

    public void downloadLog(String logPath, String prefix, HttpServletResponse response, String nodeIp, String fileName) {
        this.downloadLog(logPath, prefix, response, nodeIp, fileName, "");
    }

    public void downloadLog(String logPath, String prefix, HttpServletResponse response, String nodeIp, String fileName, String aliasFilename) {
        try {
            if (StringUtils.isBlank(aliasFilename)) {
                aliasFilename = fileName;
            }
            Escaper pathEscapers = UrlEscapers.urlPathSegmentEscaper();
            String logagentIp = k8sSvcService.getLogAgentPodIpByNodeIp(nodeIp);
            String url = "http://" + Ipv6Util.handlerIpv6Addr(logagentIp) + ":" + NetworkConstants.LOGAGENT_PORT + prefix + pathEscapers.escape(logPath);
            logger.info("download url: " + url);
            InputStream input = new URL(url).openStream();
            if (fileName.endsWith("zip") || fileName.endsWith("gz")) {
                response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(aliasFilename, "UTF-8"));
            } else {
                response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(aliasFilename + ".zip", "UTF-8"));
            }
            org.apache.commons.io.IOUtils.copy(input, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            logger.info(ExceptionUtils.getStackTrace(e));
        }
    }

    public ArrayNode getClientDirInfo(String logPath) {
        List<com.xylink.manager.model.deploy.Node> nodes = deployService.listNodesByAppLabel("logserver");
        // 过滤状态为 Ready 的节点
        Optional<String> nodeIpOptional = nodes.stream()
                .filter(com.xylink.manager.model.deploy.Node::isReady) // 检查节点是否为 Ready 状态
                .findFirst()
                .map(com.xylink.manager.model.deploy.Node::getIp); // 获取第一个符合条件的节点 IP

        String nodeIp = nodeIpOptional.orElse(null);
        if (StringUtils.isBlank(nodeIp)) {
            return objectMapper.createArrayNode();
        }
        String logagentPodIp = k8sSvcService.getLogAgentPodIpByNodeIp(nodeIp);
        if (StringUtils.isEmpty(logagentPodIp))
            return objectMapper.createArrayNode();
        try {
            String clientLogUrl = "http://" + Ipv6Util.handlerIpv6Addr(logagentPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + CLIENT_LOG_PREFIX + logPath;
            String jsonStr = restTemplate.getForObject(clientLogUrl, String.class);
            logger.info(jsonStr);
            return filterRootInfo(jsonStr, MAIN_CLIENT_LOG_ROOT);
        } catch (Exception e) {
            logger.info(ExceptionUtils.getStackTrace(e));
        }
        return objectMapper.createArrayNode();
    }

    public LogAgentConfig getLogConfig() {
        LogAgentConfig config = new LogAgentConfig();
        if (SystemModeConfig.isThirdK8s()){
            ConfigMap logExpiredDayMap = k8sThirdDeployService.getConfigMapByName("private-logagent-config");
            if (null != logExpiredDayMap) {
                String logExpiredDay = logExpiredDayMap.getData().get("XYLINK_LOGAGENT_EXPIRED_DAY");
                if (null == logExpiredDay) {
                    logExpiredDay = "30";
                }
                config.setDays(Integer.parseInt(logExpiredDay));
                String logCompress = logExpiredDayMap.getData().get("XYLINK_LOGAGENT_IS_COMPRESS");
                if (null == logCompress){
                    logCompress = "true";
                }
                config.setCompress(Boolean.parseBoolean(logCompress));
            }

        }else {
            DaemonSet ds = deployService.getDaemonSetByName("private-logagent", Constants.NAMESPACE_DEFAULT);
            Map<String, String> envMap = ds.getContainers().get(0).getEnv();
            if (envMap != null) {
                if (envMap.containsKey("XYLINK_LOGAGENT_EXPIRED_DAY")) {
                    config.setDays(Integer.parseInt(envMap.get("XYLINK_LOGAGENT_EXPIRED_DAY")));
                }
                if (envMap.containsKey("XYLINK_LOGAGENT_IS_COMPRESS")) {
                    config.setCompress(Boolean.parseBoolean(envMap.get("XYLINK_LOGAGENT_IS_COMPRESS")));
                }
            }
        }
        return config;
    }

    public void configLog(LogAgentConfig config) {
        if (SystemModeConfig.isThirdK8s()){
            k8sThirdDeployService.patchConfigMap( "private-logagent-config", Constants.NAMESPACE_DEFAULT, map -> {
                map.put("XYLINK_LOGAGENT_EXPIRED_DAY", config.getDays().toString());
                map.put("XYLINK_LOGAGENT_IS_COMPRESS", config.getCompress().toString());
            });
        }else {
            DaemonSet ds = deployService.getDaemonSetByName("private-logagent", Constants.NAMESPACE_DEFAULT);
            if (ds == null) {
                return;
            }
            ds.getContainers().get(0).getEnv().put("XYLINK_LOGAGENT_EXPIRED_DAY", config.getDays().toString());
            ds.getContainers().get(0).getEnv().put("XYLINK_LOGAGENT_IS_COMPRESS", config.getCompress().toString());
            deployService.patchDaemonSetEnv("private-logagent", Constants.NAMESPACE_DEFAULT, ds.getContainers());
        }

    }

    /**
     * 过滤绝对地址
     *
     * @return ArrayNode
     */
    private ArrayNode filterRootInfo(String jsonStr, String... root) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            if (jsonNode.isArray()) {
                ArrayNode arrayNode = (ArrayNode) jsonNode;
                for (JsonNode node : arrayNode) {
                    if (node.isObject()) {
                        ObjectNode obj = (ObjectNode) node;
                        JsonNode pathNode = obj.get("Path");
                        if (pathNode != null && pathNode.isTextual()) {
                            String path = pathNode.asText();
                            if (StringUtils.isNotBlank(path)) {
                                for (String s : root) {
                                    obj.put("Path", path.replace(s, ""));
                                }
                            }
                        }
                    }
                }
                return arrayNode;
            }
        } catch (Exception e) {
            logger.error("Failed to filter root info", e);
        }
        return objectMapper.createArrayNode();
    }
}