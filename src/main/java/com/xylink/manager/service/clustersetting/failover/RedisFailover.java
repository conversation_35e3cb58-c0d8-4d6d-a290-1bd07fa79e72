package com.xylink.manager.service.clustersetting.failover;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.RedisConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.CollectionSortUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.xylink.manager.service.redis.RedisAvailabilityService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/12/6 5:41 下午
 */
@Component
@Slf4j
public class RedisFailover implements Failover<String,String> {

    @Autowired
    private K8sService k8sService;

    @Autowired
    private RedisAvailabilityService redisAvailabilityService;

    @Override
    public Optional<String> working() {
        Map<String, String> allRedis = k8sService.getConfigmap(Constants.CONFIGMAP_REDIS);
        String sentinelHosts = allRedis.get(RedisConstants.REDIS_SENTINEL_HOSTS);
        if ("false".equals(allRedis.get(RedisConstants.REDIS_SENTINEL_ENABLE)) || StringUtils.isBlank(sentinelHosts)) {
            throw new ServiceErrorException(ErrorStatus.REDIS_SENTINEL_HOST_BLANK);
        }

        String[] hosts = sentinelHosts.split(",");
        List<String> selectedMasterIp = Arrays.stream(hosts).parallel().map(this::getRedisMasterIp).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(selectedMasterIp)) {
            throw new ServiceErrorException(ErrorStatus.GET_REDIS_MASTER_IP_FAILED);
        }

        List<Map.Entry<String, Integer>> entryList = CollectionSortUtil.sortStrListByStrNum(selectedMasterIp,true);
        log.info("redis master ip: {}", entryList.get(0).getKey());
        return Optional.of(entryList.get(0).getKey());
    }

    private String getRedisMasterIp(String host) {
        String[] ipAndPort = host.split(":");

        try {
            // 使用统一Redis服务检查Redis连接并获取主节点信息
            // 注意：这里简化处理，实际哨兵模式可能需要更复杂的逻辑
            String hostIp = ipAndPort[0];
            int port = Integer.parseInt(ipAndPort[1]);

            // 测试连接并检查是否为主节点
            if (redisAvailabilityService.testConnection(hostIp, port, null, null)) {
                if (redisAvailabilityService.isRedisMaster(hostIp, port, null, null)) {
                    log.info("connect redis sentinel: {} get redis master ip: {}", host, hostIp);
                    return hostIp;
                }
            }
        } catch (Exception e) {
            log.error("connect redis sentinel: {} to get master failed", host, e);
        }
        return null;
    }

    @Override
    public void setWorking(String t) {
        Map<String, String> allRedis = k8sService.getConfigmap(Constants.CONFIGMAP_REDIS);

        String oldRedisMasterHostname = "";
        String newRedisMasterHostname = "";
        for (Map.Entry<String, String> entry : allRedis.entrySet()) {
            //找到旧master hostname
            if (RedisConstants.REDIS_ROLE_MASTER.equals(entry.getValue())) {
                oldRedisMasterHostname = entry.getKey().replace("-REDIS-ROLE", "");
                continue;
            }

            //找到新master hostname
            if (entry.getKey().contains(NetworkConstants.SUFFIX_INTERNAL_IP) && t.equals(entry.getValue())) {
                newRedisMasterHostname = entry.getKey().replace("-INTERNAL-IP", "");
            }
        }

        //1、更改对应新redis节点角色
        if (StringUtils.isNotBlank(oldRedisMasterHostname)) {
            allRedis.put(oldRedisMasterHostname + RedisConstants.REDIS_ROLE_SUFFIX, RedisConstants.REDIS_ROLE_SLAVE);
        }

        allRedis.put(newRedisMasterHostname + RedisConstants.REDIS_ROLE_SUFFIX, RedisConstants.REDIS_ROLE_MASTER);
        allRedis.put(NetworkConstants.REDIS_MASTER_IP, t);

        k8sService.editConfigmap(Constants.CONFIGMAP_REDIS, allRedis);
    }
}
