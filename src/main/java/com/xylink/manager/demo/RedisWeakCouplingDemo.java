package com.xylink.manager.demo;

import com.xylink.manager.service.redis.RedisAvailabilityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Redis弱关联演示程序
 * 
 * 演示Redis弱关联配置的效果：
 * 1. 当Redis启用时，展示完整的Redis功能
 * 2. 当Redis禁用时，展示降级处理效果
 * 
 * 使用方式：
 * - 启用演示：--demo.redis.enabled=true
 * - 禁用演示：--demo.redis.enabled=false 或不设置
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "demo.redis.enabled", havingValue = "true", matchIfMissing = false)
public class RedisWeakCouplingDemo implements CommandLineRunner {

    @Autowired
    private RedisAvailabilityService redisAvailabilityService;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== Redis弱关联配置演示开始 ===");
        
        // 检查Redis可用性
        boolean redisAvailable = redisAvailabilityService.isRedisAvailable();
        log.info("Redis可用性检查: {}", redisAvailable ? "可用" : "不可用");
        
        // 演示各种Redis操作的降级处理
        demonstrateConnectionTest();
        demonstrateRedisInfo();
        demonstrateMasterCheck();
        demonstrateMemoryInfo();
        demonstrateStatsInfo();
        demonstratePing();
        demonstrateVersionCheck();
        
        log.info("=== Redis弱关联配置演示结束 ===");
    }

    private void demonstrateConnectionTest() {
        log.info("--- 连接测试演示 ---");
        boolean connected = redisAvailabilityService.testConnection("localhost", 6379, null, null);
        log.info("连接测试结果: {}", connected ? "连接成功" : "连接失败（降级处理）");
    }

    private void demonstrateRedisInfo() {
        log.info("--- Redis信息获取演示 ---");
        Map<String, String> redisInfo = redisAvailabilityService.getRedisInfo(null);
        if (redisInfo.isEmpty()) {
            log.info("Redis信息: 空（降级处理）");
        } else {
            log.info("Redis信息: 获取到{}个字段", redisInfo.size());
            // 只显示几个关键字段
            redisInfo.entrySet().stream()
                .limit(3)
                .forEach(entry -> log.info("  {}: {}", entry.getKey(), entry.getValue()));
        }
    }

    private void demonstrateMasterCheck() {
        log.info("--- 主节点检查演示 ---");
        boolean isMaster = redisAvailabilityService.isRedisMaster("localhost", 6379, null, null);
        log.info("主节点检查结果: {}", isMaster ? "是主节点" : "不是主节点（或降级处理）");
    }

    private void demonstrateMemoryInfo() {
        log.info("--- 内存信息获取演示 ---");
        Map<String, String> memoryInfo = redisAvailabilityService.getMemoryInfo();
        if (memoryInfo.isEmpty()) {
            log.info("内存信息: 空（降级处理）");
        } else {
            log.info("内存信息: 获取到{}个字段", memoryInfo.size());
            // 显示内存相关字段
            String usedMemory = memoryInfo.get("used_memory_human");
            if (usedMemory != null) {
                log.info("  已用内存: {}", usedMemory);
            }
        }
    }

    private void demonstrateStatsInfo() {
        log.info("--- 统计信息获取演示 ---");
        Map<String, String> statsInfo = redisAvailabilityService.getStatsInfo();
        if (statsInfo.isEmpty()) {
            log.info("统计信息: 空（降级处理）");
        } else {
            log.info("统计信息: 获取到{}个字段", statsInfo.size());
            // 显示统计相关字段
            String totalConnections = statsInfo.get("total_connections_received");
            if (totalConnections != null) {
                log.info("  总连接数: {}", totalConnections);
            }
        }
    }

    private void demonstratePing() {
        log.info("--- PING命令演示 ---");
        String pingResult = redisAvailabilityService.ping();
        log.info("PING结果: {}", pingResult);
    }

    private void demonstrateVersionCheck() {
        log.info("--- 版本检查演示 ---");
        String version = redisAvailabilityService.getRedisVersion();
        log.info("Redis版本: {}", version);
    }
}
