package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/09/14/11:44
 * @Description:
 */
public class BasicBaseHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(BasicBaseHandler.class);


    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }

        if (node == null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, Labels.basic_base.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());

        if (enableLabels.contains(Labels.inspection.label())) {
            allIpMap.put("INSPECTION_IP", node.getInternalIp());
        }
        if (enableLabels.contains(Labels.message_push.label())) {
            allIpMap.put("MESSAGE_PUSH_IP", node.getInternalIp());
        }
        allIpMap.put(NetworkConstants.BASE_IP, getDistributeIp(Labels.frontend.label()));
        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.basic_base.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));


        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }

    /**
     * 和标准流程不一样的是  默认不部署该节点下的服务
     *
     * @return
     */
    @Override
    protected NodeHandler initDefaultNodeConfig() {
        // 默认部署了改节点类型下的所有服务
        super.initDefaultNodeConfig();
        Map<String, Boolean> labels = new HashMap<>();
        String type = deployMessage.getType();
        DefaultDeployStructureEnumInvoke.services(type).forEach(label -> labels.put(label, type.equals(label)));
        this.node.setLabelMap(labels);
        return this;
    }
}
