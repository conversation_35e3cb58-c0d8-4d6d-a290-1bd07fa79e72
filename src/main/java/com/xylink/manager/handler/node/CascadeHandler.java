package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.SystemModeConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/04/18/17:39
 * @Description:
 */
public class CascadeHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(CascadeHandler.class);

    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, node.getType());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        if (node.getLabelMap().get(Labels.cascadegw.label())) {
            allIpMap.put(NetworkConstants.CASCADEGW_IP, node.getInternalIp());
        }
        if (node.getLabelMap().get(Labels.avstatusserver.label())) {
            allIpMap.put(NetworkConstants.AVSTATUS_IP, node.getInternalIp());
        }
        if (node.getLabelMap().get(Labels.roster.label())) {
            allIpMap.put(NetworkConstants.ROSTER_IP1, node.getInternalIp());
        }
        //多节点服务
        DefaultDeployStructureEnumInvoke.services(node.getType())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.cascade.label());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(node.getType()).forEach(label -> labelMap.put(label, true));
        if (SystemModeConfig.isCmsOrXms()) {
            labelMap.put(Labels.cascadegw.label(), false);
        }
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}

