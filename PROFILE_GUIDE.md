# Spring Profile 配置指南

本项目支持基于Spring Profile的本地开发配置，实现本地调试和服务端部署的配置分离。

## 📁 配置文件结构

```
src/main/resources/
├── application.properties          # 主配置文件（默认配置，服务端使用）
└── application-local.properties    # 本地开发环境配置
```

## 🚀 启动方式

### 1. 本地开发环境（使用local profile）

#### 方式一：使用启动脚本（推荐）
```bash
# Linux/macOS
./start-local.sh

# Windows
start-local.bat
```

#### 方式二：使用Gradle命令
```bash
# 指定profile参数
./gradlew bootRun -Pprofile=local

# 或使用系统属性
./gradlew bootRun -Dspring.profiles.active=local
```

#### 方式三：IDE启动
在IDE中设置VM参数：
```
-Dspring.profiles.active=local
```

### 2. 服务端环境（默认模式，保持现状）

服务端部署时无需修改任何启动参数，使用原始配置：
```bash
# 原有的启动方式保持不变，不使用Profile
java -jar manager.war

# 或使用Gradle（默认模式）
./gradlew bootRun
```

## ⚙️ 配置说明

### 本地开发环境 (local profile)
`application-local.properties` 中的配置会覆盖 `application.properties` 中的对应配置：

- **K8s协议**: `k8s.protocol.default.http=false`
- **日志级别**: `logging.level.com.xylink=debug` (更详细的调试日志)
- **基础路径**: `base.dir=/tmp/xylink` (本地临时目录)
- **脚本路径**: `server.script.dir=${user.home}/work/code/manager/src/main/resources/scripts/`
- **文件存储**: `/tmp/dev` (本地开发目录)
- **告警间隔**: 更短的检查间隔用于快速测试

### 默认环境 (无profile)
直接使用 `application.properties` 中的原始配置，保持服务端部署的完全兼容性。

## 🔧 配置原理

### Profile工作机制
- **无Profile**: 只加载 `application.properties`
- **local Profile**: 加载 `application.properties` + `application-local.properties`
- **配置覆盖**: local配置会覆盖同名的默认配置

### 配置优先级
Spring Boot配置加载优先级（从高到低）：
1. 命令行参数
2. 系统属性
3. `application-local.properties` (当激活local profile时)
4. `application.properties`

## 📝 使用建议

### 1. 本地开发
- 使用 `./start-local.sh` 或 `./gradlew bootRun -Pprofile=local`
- 在 `application-local.properties` 中添加本地特定配置
- 避免修改 `application.properties`

### 2. 服务端部署
- 保持原有启动方式不变
- 不需要添加任何Profile参数
- 确保向后兼容性

### 3. 添加新配置
如需添加本地特定配置，在 `application-local.properties` 中添加：
```properties
# 示例：本地数据库配置
spring.datasource.url=***************************************
spring.datasource.username=dev_user
spring.datasource.password=dev_password
```

## 🔍 验证配置

### 查看启动信息
启动时控制台会显示：
- **本地模式**: "=== 本地开发模式启动 === Profile: local"
- **默认模式**: "=== 默认模式启动 === 使用原始 application.properties 配置"

### 测试配置
```bash
# 测试本地模式
./gradlew bootRun -Pprofile=local --dry-run

# 测试默认模式
./gradlew bootRun --dry-run
```

## 🎯 总结

这个简化的Profile方案实现了：
- ✅ **本地开发**: 使用 `local` profile 加载本地配置
- ✅ **服务端部署**: 保持原有方式不变，无需修改
- ✅ **配置分离**: 本地配置独立，不影响服务端
- ✅ **向后兼容**: 服务端部署完全兼容现有方式
- ✅ **简单易用**: 只需要在本地开发时添加 `local` 参数
