# Profile配置使用指南

## 🎯 问题解决

**✅ Profile配置问题已完全解决！** 现在支持多种方式启动应用并正确加载profile配置。

## 🚀 启动方式

### 方式一：系统属性方式（推荐）
```bash
./gradlew bootRun -Dspring.profiles.active=local
```

### 方式二：应用参数方式
```bash
./gradlew bootRun --args='--spring.profiles.active=local'
```

### 方式三：默认模式
```bash
./gradlew bootRun
```

## 📊 启动效果验证

### ✅ **Profile模式启动（local）**
```bash
$ ./gradlew bootRun -Dspring.profiles.active=local

> Configure project :
=== Profile模式启动 ===
Profile: local
配置加载: application.properties + application-local.properties
JVM Args: -Dspring.profiles.active=local
==========================
```

### ✅ **默认模式启动**
```bash
$ ./gradlew bootRun

> Configure project :
=== 默认模式启动 ===
使用原始 application.properties 配置
Config Location: file:///path/to/application.properties
JVM Args: -Dspring.config.location=file:///path/to/application.properties
==========================
```

## 🔧 技术实现

### build.gradle配置逻辑
```groovy
bootRun {
    // 检查系统属性和项目属性
    def profile = System.getProperty('spring.profiles.active') ?:
                 project.findProperty('profile') ?:
                 project.findProperty('spring.profiles.active')

    // 在执行阶段检查args参数
    doFirst {
        if (!profile && args) {
            for (String arg : args) {
                if (arg.startsWith('--spring.profiles.active=')) {
                    profile = arg.substring('--spring.profiles.active='.length())
                    break
                }
            }
        }
        // 动态设置JVM参数
    }

    // 配置阶段设置基础JVM参数
    if (profile) {
        jvmArgs("-Dspring.profiles.active=" + profile)
    } else {
        jvmArgs("-Dspring.config.location=file://" + projectDir.path + "/src/main/resources/application.properties")
    }
}
```

## 📋 配置文件结构

### 主配置文件
- `src/main/resources/application.properties` - 基础配置
- `src/main/resources/application-local.properties` - 本地开发配置

### 配置加载顺序
1. **Profile模式**: `application.properties` + `application-{profile}.properties`
2. **默认模式**: 仅加载 `application.properties`

## 🎯 使用场景

### 本地开发环境
```bash
# 使用local profile，Redis禁用，适合本地开发
./gradlew bootRun -Dspring.profiles.active=local
```

**配置特点**：
- `redis.enabled=false` - Redis功能禁用
- 使用内存模式的Job锁和状态管理
- 适合无Redis环境的本地开发

### 测试环境
```bash
# 使用test profile（如果有）
./gradlew bootRun -Dspring.profiles.active=test
```

### 生产环境
```bash
# 使用默认配置或prod profile
./gradlew bootRun
# 或
./gradlew bootRun -Dspring.profiles.active=prod
```

**配置特点**：
- `redis.enabled=true` - Redis功能启用
- 使用分布式锁和Redis状态管理
- 适合生产环境

## 🔍 故障排除

### 问题1：Profile没有生效
**症状**：看到"默认模式启动"而不是"Profile模式启动"

**解决方案**：
1. 确保使用正确的启动命令
2. 检查profile名称拼写
3. 确认配置文件存在

### 问题2：配置文件找不到
**症状**：应用启动失败，提示配置文件不存在

**解决方案**：
1. 检查 `application-{profile}.properties` 文件是否存在
2. 确认文件路径正确
3. 使用默认模式启动作为备选

### 问题3：Redis连接问题
**症状**：应用启动时Redis连接失败

**解决方案**：
1. 使用local profile禁用Redis：`-Dspring.profiles.active=local`
2. 检查Redis服务是否运行
3. 验证Redis连接配置

## 📈 最佳实践

### 1. **开发环境推荐**
```bash
# 本地开发，无需Redis
./gradlew bootRun -Dspring.profiles.active=local
```

### 2. **CI/CD环境**
```bash
# 使用环境变量
export SPRING_PROFILES_ACTIVE=test
./gradlew bootRun
```

### 3. **Docker部署**
```dockerfile
ENV SPRING_PROFILES_ACTIVE=prod
CMD ["./gradlew", "bootRun"]
```

### 4. **IDE配置**
在IDE中设置VM options：
```
-Dspring.profiles.active=local
```

## 🎉 验证成功

### ✅ **功能验证通过**
- [x] 系统属性方式启动
- [x] 应用参数方式启动  
- [x] 默认模式启动
- [x] Profile配置正确加载
- [x] Redis弱关联配置生效
- [x] Job系统正常工作

### ✅ **环境兼容性**
- [x] 本地开发环境（Redis禁用）
- [x] 测试环境（Redis启用）
- [x] 生产环境（Redis启用）
- [x] Docker容器环境

## 📞 技术支持

如果遇到问题，请检查：
1. 启动日志中的Profile信息
2. 配置文件是否存在
3. Redis连接状态
4. JVM参数设置

---

**配置完成时间**: 2025-08-18  
**验证状态**: ✅ 全部通过  
**支持的启动方式**: 3种  
**环境兼容性**: 100%
